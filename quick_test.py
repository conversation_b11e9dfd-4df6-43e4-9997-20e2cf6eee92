#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本 - 验证整个流程
"""

import torch
import numpy as np
import pandas as pd
from torch.utils.data import DataLoader, TensorDataset
import os
import pickle
from scipy.io import savemat

from wgan_gp_model import WGAN_GP, load_training_data
from evaluation_metrics import EvaluationMetrics

def quick_test():
    """快速测试整个流程"""
    print("=== 快速测试WiFi指纹数据生成流程 ===")
    
    # 1. 加载数据
    print("1. 加载训练数据...")
    training_data = load_training_data()
    print(f"训练数据形状: {training_data.shape}")
    
    # 2. 创建模型
    print("2. 创建WGAN-GP模型...")
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    model = WGAN_GP(device=device)
    model.set_optimizers(lr=0.0001)
    
    # 3. 快速训练（只训练几个epoch）
    print("3. 快速训练模型...")
    tensor_data = torch.FloatTensor(training_data).to(device)
    dataset = TensorDataset(tensor_data)
    dataloader = DataLoader(dataset, batch_size=64, shuffle=True)
    
    epochs = 5  # 只训练5个epoch进行测试
    for epoch in range(epochs):
        epoch_losses = []
        
        for batch_idx, (real_data,) in enumerate(dataloader):
            real_data = real_data.to(device)
            
            # 训练步骤
            losses = model.train_step(real_data, lambda_gp=10.0, lambda_physical=1.0)
            epoch_losses.append(losses)
            
            if batch_idx >= 5:  # 只训练前5个batch
                break
        
        avg_losses = {key: np.mean([loss[key] for loss in epoch_losses]) 
                     for key in epoch_losses[0].keys()}
        
        print(f"Epoch {epoch+1}/{epochs} - "
              f"D_loss: {avg_losses['d_loss']:.4f}, "
              f"G_loss: {avg_losses['g_loss']:.4f}")
    
    # 4. 生成样本
    print("4. 生成样本...")
    num_samples = 100
    generated_samples = model.generate_samples(num_samples)
    print(f"生成样本形状: {generated_samples.shape}")
    print(f"生成样本范围: {generated_samples.min():.2f} 到 {generated_samples.max():.2f}")
    
    # 5. 评估质量
    print("5. 评估生成质量...")
    evaluator = EvaluationMetrics()
    real_samples = training_data[:num_samples]
    results = evaluator.evaluate_all(real_samples, generated_samples)
    is_acceptable = evaluator.is_acceptable_quality(results)
    
    # 6. 反标准化数据
    print("6. 反标准化数据...")
    with open('processed_data/normalization_params.pkl', 'rb') as f:
        norm_params = pickle.load(f)
    
    # 反标准化
    denormalized = generated_samples.copy()
    mask = generated_samples != -100
    denormalized[mask] = (generated_samples[mask] * norm_params['std'] + norm_params['mean'])
    denormalized[~mask] = -100
    denormalized[denormalized > 0] = -100  # 确保正数变为-100
    
    print(f"反标准化后范围: {denormalized.min():.2f} 到 {denormalized.max():.2f}")
    
    # 7. 创建完整的数据集
    print("7. 创建完整的数据集...")
    wap_cols = [f'WAP{i+1:03d}' for i in range(520)]
    generated_df = pd.DataFrame(denormalized, columns=wap_cols)
    
    # 添加位置信息
    original_data = pd.read_csv('processed_data/building0_floor3_processed.csv')
    position_cols = ['LONGITUDE', 'LATITUDE']
    position_indices = np.random.choice(len(original_data), num_samples, replace=True)
    position_data = original_data[position_cols].iloc[position_indices].reset_index(drop=True)
    
    final_generated_data = pd.concat([generated_df, position_data], axis=1)
    
    # 8. 合并原始数据和生成数据
    print("8. 合并数据...")
    original_subset = original_data.iloc[:50]  # 只取50条原始数据进行测试
    merged_data = pd.concat([original_subset, final_generated_data], ignore_index=True)
    
    # 确保100值变为-100
    for col in wap_cols:
        merged_data[col] = merged_data[col].replace(100, -100)
        merged_data.loc[merged_data[col] > 0, col] = -100
    
    print(f"合并后数据形状: {merged_data.shape}")
    
    # 9. 保存数据
    print("9. 保存数据...")
    os.makedirs('test_output', exist_ok=True)
    
    # 保存CSV
    merged_data.to_csv('test_output/test_merged_data.csv', index=False)
    
    # 保存MAT
    mat_data = {
        'data': merged_data.values,
        'columns': merged_data.columns.tolist(),
        'shape': merged_data.shape
    }
    savemat('test_output/test_merged_data.mat', mat_data)
    
    print("=== 快速测试完成 ===")
    print(f"生成质量是否可接受: {'是' if is_acceptable else '否'}")
    print("测试数据已保存到 test_output/ 目录")
    print(f"最终数据集包含 {len(merged_data)} 条记录，{len(wap_cols)} 个WAP特征 + 2个位置特征")
    
    return merged_data, results

if __name__ == "__main__":
    merged_data, results = quick_test()
