#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建结果可视化 - 生成优化结果图表
"""

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sns
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def create_optimization_results_chart():
    """创建优化结果图表"""
    
    # 优化前后的评估指标数据
    original_results = {
        'kl_divergence': 18.2491,
        'js_divergence': 0.6916,
        'wasserstein_distance': 10.6634,
        'mmd': 1.0020,
        'coverage': 0.5000,
        'density': 239.2306
    }
    
    optimized_results = {
        'kl_divergence': 15.5886,
        'js_divergence': 0.6840,
        'wasserstein_distance': 161.6060,
        'mmd': 0.0040,
        'coverage': 0.5000,
        'density': 4063.2533
    }
    
    # 创建图表
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('WiFi指纹数据集优化结果分析', fontsize=20, fontweight='bold', y=0.98)
    
    # 1. 主要评估指标对比
    ax1 = axes[0, 0]
    metrics = ['KL散度', 'JS散度', 'Wasserstein距离']
    original_values = [
        original_results['kl_divergence'],
        original_results['js_divergence'],
        original_results['wasserstein_distance']
    ]
    optimized_values = [
        optimized_results['kl_divergence'],
        optimized_results['js_divergence'],
        optimized_results['wasserstein_distance']
    ]
    targets = [0.2, 0.2, 2.0]
    
    x = np.arange(len(metrics))
    width = 0.25
    
    bars1 = ax1.bar(x - width, original_values, width, label='优化前', alpha=0.8, color='#ff6b6b')
    bars2 = ax1.bar(x, optimized_values, width, label='优化后', alpha=0.8, color='#4ecdc4')
    bars3 = ax1.bar(x + width, targets, width, label='目标值', alpha=0.8, color='#45b7d1')
    
    ax1.set_title('主要评估指标对比', fontweight='bold', fontsize=14)
    ax1.set_xlabel('指标')
    ax1.set_ylabel('数值')
    ax1.set_xticks(x)
    ax1.set_xticklabels(metrics)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.set_yscale('log')  # 使用对数刻度以便更好显示
    
    # 添加数值标签
    for bars in [bars1, bars2, bars3]:
        for bar in bars:
            height = bar.get_height()
            ax1.annotate(f'{height:.2f}',
                        xy=(bar.get_x() + bar.get_width() / 2, height),
                        xytext=(0, 3),
                        textcoords="offset points",
                        ha='center', va='bottom', fontsize=8)
    
    # 2. KL散度详细分析
    ax2 = axes[0, 1]
    kl_data = [original_results['kl_divergence'], optimized_results['kl_divergence'], 0.2]
    kl_labels = ['优化前', '优化后', '目标值']
    colors = ['#ff6b6b', '#4ecdc4', '#45b7d1']
    
    bars = ax2.bar(kl_labels, kl_data, color=colors, alpha=0.8)
    ax2.set_title('KL散度对比', fontweight='bold', fontsize=14)
    ax2.set_ylabel('KL散度')
    ax2.axhline(y=0.2, color='red', linestyle='--', alpha=0.7, linewidth=2)
    
    for bar in bars:
        height = bar.get_height()
        ax2.annotate(f'{height:.3f}',
                    xy=(bar.get_x() + bar.get_width() / 2, height),
                    xytext=(0, 3),
                    textcoords="offset points",
                    ha='center', va='bottom', fontsize=12, fontweight='bold')
    
    # 添加达标状态
    kl_status = "❌ 未达标" if optimized_results['kl_divergence'] > 0.2 else "✅ 达标"
    ax2.text(0.5, 0.95, kl_status, transform=ax2.transAxes, 
             ha='center', va='top', fontsize=12, fontweight='bold',
             color='red' if optimized_results['kl_divergence'] > 0.2 else 'green')
    
    ax2.grid(True, alpha=0.3)
    
    # 3. JS散度详细分析
    ax3 = axes[0, 2]
    js_data = [original_results['js_divergence'], optimized_results['js_divergence'], 0.2]
    
    bars = ax3.bar(kl_labels, js_data, color=colors, alpha=0.8)
    ax3.set_title('JS散度对比', fontweight='bold', fontsize=14)
    ax3.set_ylabel('JS散度')
    ax3.axhline(y=0.2, color='red', linestyle='--', alpha=0.7, linewidth=2)
    
    for bar in bars:
        height = bar.get_height()
        ax3.annotate(f'{height:.3f}',
                    xy=(bar.get_x() + bar.get_width() / 2, height),
                    xytext=(0, 3),
                    textcoords="offset points",
                    ha='center', va='bottom', fontsize=12, fontweight='bold')
    
    # 添加达标状态
    js_status = "❌ 未达标" if optimized_results['js_divergence'] > 0.2 else "✅ 达标"
    ax3.text(0.5, 0.95, js_status, transform=ax3.transAxes, 
             ha='center', va='top', fontsize=12, fontweight='bold',
             color='red' if optimized_results['js_divergence'] > 0.2 else 'green')
    
    ax3.grid(True, alpha=0.3)
    
    # 4. Wasserstein距离详细分析
    ax4 = axes[1, 0]
    wd_data = [original_results['wasserstein_distance'], optimized_results['wasserstein_distance'], 2.0]
    
    bars = ax4.bar(kl_labels, wd_data, color=colors, alpha=0.8)
    ax4.set_title('Wasserstein距离对比', fontweight='bold', fontsize=14)
    ax4.set_ylabel('Wasserstein距离')
    ax4.axhline(y=2.0, color='red', linestyle='--', alpha=0.7, linewidth=2)
    
    for bar in bars:
        height = bar.get_height()
        ax4.annotate(f'{height:.1f}',
                    xy=(bar.get_x() + bar.get_width() / 2, height),
                    xytext=(0, 3),
                    textcoords="offset points",
                    ha='center', va='bottom', fontsize=12, fontweight='bold')
    
    # 添加达标状态
    wd_status = "❌ 未达标" if optimized_results['wasserstein_distance'] > 2.0 else "✅ 达标"
    ax4.text(0.5, 0.95, wd_status, transform=ax4.transAxes, 
             ha='center', va='top', fontsize=12, fontweight='bold',
             color='red' if optimized_results['wasserstein_distance'] > 2.0 else 'green')
    
    ax4.grid(True, alpha=0.3)
    
    # 5. 改进程度分析
    ax5 = axes[1, 1]
    
    # 计算改进程度
    kl_improvement = ((original_results['kl_divergence'] - optimized_results['kl_divergence']) / 
                     original_results['kl_divergence']) * 100
    js_improvement = ((original_results['js_divergence'] - optimized_results['js_divergence']) / 
                     original_results['js_divergence']) * 100
    wd_improvement = ((original_results['wasserstein_distance'] - optimized_results['wasserstein_distance']) / 
                     original_results['wasserstein_distance']) * 100
    
    improvements = [kl_improvement, js_improvement, wd_improvement]
    improvement_labels = ['KL散度', 'JS散度', 'Wasserstein距离']
    
    colors_imp = ['green' if imp > 0 else 'red' for imp in improvements]
    bars = ax5.bar(improvement_labels, improvements, color=colors_imp, alpha=0.8)
    
    ax5.set_title('改进程度分析', fontweight='bold', fontsize=14)
    ax5.set_ylabel('改进百分比 (%)')
    ax5.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    
    for bar in bars:
        height = bar.get_height()
        ax5.annotate(f'{height:.1f}%',
                    xy=(bar.get_x() + bar.get_width() / 2, height),
                    xytext=(0, 3 if height >= 0 else -15),
                    textcoords="offset points",
                    ha='center', va='bottom' if height >= 0 else 'top', 
                    fontsize=12, fontweight='bold')
    
    ax5.grid(True, alpha=0.3)
    
    # 6. 总体评估摘要
    ax6 = axes[1, 2]
    
    # 检查达标状态
    kl_target = optimized_results['kl_divergence'] < 0.2
    js_target = optimized_results['js_divergence'] < 0.2
    wd_target = optimized_results['wasserstein_distance'] < 2.0
    
    target_status = [kl_target, js_target, wd_target]
    target_labels = ['KL散度\n<0.2', 'JS散度\n<0.2', 'Wasserstein\n<2.0']
    
    colors_target = ['green' if status else 'red' for status in target_status]
    symbols = ['✅' if status else '❌' for status in target_status]
    
    bars = ax6.bar(target_labels, [1]*3, color=colors_target, alpha=0.8)
    
    for i, (bar, symbol) in enumerate(zip(bars, symbols)):
        ax6.text(bar.get_x() + bar.get_width()/2, 0.5, symbol, 
                ha='center', va='center', fontsize=24, fontweight='bold')
    
    ax6.set_title('目标达成状态', fontweight='bold', fontsize=14)
    ax6.set_ylim(0, 1)
    ax6.set_yticks([])
    
    # 总体状态
    targets_met = sum(target_status)
    overall_text = f"📊 {targets_met}/3 项指标达标"
    if targets_met == 3:
        overall_text = "🎉 全部指标达标!"
        overall_color = 'lightgreen'
    elif targets_met >= 1:
        overall_text = f"⚠️ {targets_met}/3 项指标达标"
        overall_color = 'lightyellow'
    else:
        overall_text = "❌ 暂未达标"
        overall_color = 'lightcoral'
    
    ax6.text(0.5, 1.15, overall_text, transform=ax6.transAxes, 
            ha='center', va='bottom', fontsize=14, fontweight='bold',
            bbox=dict(boxstyle="round,pad=0.5", facecolor=overall_color))
    
    plt.tight_layout()
    
    # 保存图表
    os.makedirs('optimization_results', exist_ok=True)
    plt.savefig('optimization_results/optimization_results_chart.png', dpi=300, bbox_inches='tight')
    print("✅ 优化结果图表已保存: optimization_results/optimization_results_chart.png")
    
    plt.show()
    
    return target_status

def create_summary_report():
    """创建总结报告"""
    
    # 数据
    original_results = {
        'kl_divergence': 18.2491,
        'js_divergence': 0.6916,
        'wasserstein_distance': 10.6634
    }
    
    optimized_results = {
        'kl_divergence': 15.5886,
        'js_divergence': 0.6840,
        'wasserstein_distance': 161.6060
    }
    
    print("=" * 60)
    print("WiFi指纹数据集优化结果总结报告")
    print("=" * 60)
    
    print(f"\n📊 优化前评估指标:")
    print(f"   KL散度: {original_results['kl_divergence']:.4f}")
    print(f"   JS散度: {original_results['js_divergence']:.4f}")
    print(f"   Wasserstein距离: {original_results['wasserstein_distance']:.4f}")
    
    print(f"\n📈 优化后评估指标:")
    print(f"   KL散度: {optimized_results['kl_divergence']:.4f}")
    print(f"   JS散度: {optimized_results['js_divergence']:.4f}")
    print(f"   Wasserstein距离: {optimized_results['wasserstein_distance']:.4f}")
    
    print(f"\n🎯 目标达成情况:")
    kl_status = "✅ 达标" if optimized_results['kl_divergence'] < 0.2 else "❌ 未达标"
    js_status = "✅ 达标" if optimized_results['js_divergence'] < 0.2 else "❌ 未达标"
    wd_status = "✅ 达标" if optimized_results['wasserstein_distance'] < 2.0 else "❌ 未达标"
    
    print(f"   KL散度 < 0.2: {kl_status}")
    print(f"   JS散度 < 0.2: {js_status}")
    print(f"   Wasserstein距离 < 2.0: {wd_status}")
    
    print(f"\n📋 优化策略:")
    print(f"   ✓ 将578,656个接近-89的值替换为100")
    print(f"   ✓ 调整了14,466个信号值以匹配原始分布")
    print(f"   ✓ 保持了数据集的整体结构和完整性")
    
    print(f"\n💾 输出文件:")
    print(f"   ✓ optimization_results/quick_optimized_wifi_dataset.csv")
    print(f"   ✓ optimization_results/quick_optimized_wifi_dataset.mat")
    print(f"   ✓ optimization_results/optimization_results_chart.png")
    
    targets_met = sum([
        optimized_results['kl_divergence'] < 0.2,
        optimized_results['js_divergence'] < 0.2,
        optimized_results['wasserstein_distance'] < 2.0
    ])
    
    print(f"\n🏆 总体评价:")
    if targets_met == 3:
        print(f"   🎉 优秀！所有指标均达到目标要求")
    elif targets_met >= 1:
        print(f"   ⚠️ 良好！{targets_met}/3项指标达到目标要求")
    else:
        print(f"   ❌ 需要进一步优化")
    
    print("=" * 60)

def main():
    """主函数"""
    print("=== 创建WiFi指纹数据集优化结果可视化 ===")
    
    # 创建优化结果图表
    target_status = create_optimization_results_chart()
    
    # 创建总结报告
    create_summary_report()
    
    print("\n✅ 可视化结果创建完成！")

if __name__ == "__main__":
    main()
