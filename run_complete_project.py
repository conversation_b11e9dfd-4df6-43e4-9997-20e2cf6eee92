#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整项目运行脚本 - 一键运行WiFi指纹数据集扩充项目
"""

import os
import sys
import pandas as pd
import numpy as np
import torch
from datetime import datetime
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
from scipy.io import savemat
import pickle

# 导入项目模块
from wgan_gp_model import WGAN_GP, load_training_data
from evaluation_metrics import EvaluationMetrics

class CompleteProjectRunner:
    """完整项目运行器"""
    
    def __init__(self):
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        self.evaluator = EvaluationMetrics()
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        print("=" * 80)
        print("🚀 WiFi指纹数据集扩充项目 - 完整运行")
        print("=" * 80)
        print(f"开始时间: {datetime.now()}")
        print(f"运行设备: {self.device}")
        
    def check_environment(self):
        """检查运行环境"""
        print("\n📋 步骤1: 检查运行环境")
        
        # 检查必要文件
        required_files = [
            'UJIndoorLoc/trainingData.csv',
            'processed_data/building0_floor3_training.csv',
            'processed_data/normalization_params.pkl'
        ]
        
        all_files_exist = True
        for file in required_files:
            if os.path.exists(file):
                size = os.path.getsize(file) / 1024 / 1024
                print(f"   ✅ {file} ({size:.2f} MB)")
            else:
                print(f"   ❌ {file} 不存在")
                all_files_exist = False
        
        if not all_files_exist:
            print("   ⚠️ 缺少必要文件，正在重新生成...")
            self.regenerate_data()
        
        print("   ✅ 环境检查完成")
        return True
    
    def regenerate_data(self):
        """重新生成必要的数据文件"""
        print("   🔄 重新生成数据文件...")
        
        # 如果需要，运行数据预处理
        if not os.path.exists('processed_data/building0_floor3_training.csv'):
            os.system('python data_preprocessing.py')
    
    def load_existing_model_or_train(self):
        """加载现有模型或重新训练"""
        print("\n🤖 步骤2: 模型准备")
        
        model_path = 'demo_results/wgan_gp_demo.pth'
        
        if os.path.exists(model_path):
            print(f"   ✅ 发现现有模型: {model_path}")
            model = WGAN_GP(device=self.device)
            model.load_model(model_path)
            print("   ✅ 模型加载完成")
        else:
            print("   🔄 现有模型不存在，开始快速训练...")
            model = self.quick_train_model()
        
        return model
    
    def quick_train_model(self):
        """快速训练模型"""
        from torch.utils.data import DataLoader, TensorDataset
        
        # 加载训练数据
        training_data = load_training_data()
        print(f"   📊 训练数据形状: {training_data.shape}")
        
        # 创建模型
        model = WGAN_GP(device=self.device)
        model.set_optimizers(lr=0.0002)
        
        # 创建数据加载器
        tensor_data = torch.FloatTensor(training_data).to(self.device)
        dataset = TensorDataset(tensor_data)
        dataloader = DataLoader(dataset, batch_size=64, shuffle=True)
        
        # 快速训练（20轮）
        epochs = 20
        print(f"   🏃 开始快速训练 ({epochs}轮)...")
        
        for epoch in range(epochs):
            epoch_losses = []
            
            for batch_idx, (real_data,) in enumerate(dataloader):
                real_data = real_data.to(self.device)
                
                # 训练步骤
                losses = model.train_step(real_data, lambda_gp=10.0, lambda_physical=1.0)
                epoch_losses.append(losses)
                
                # 只训练前5个batch以节省时间
                if batch_idx >= 4:
                    break
            
            if (epoch + 1) % 5 == 0:
                avg_losses = {key: np.mean([loss[key] for loss in epoch_losses]) 
                             for key in epoch_losses[0].keys()}
                print(f"   📈 Epoch {epoch+1}/{epochs} - D_loss: {avg_losses['d_loss']:.2f}, G_loss: {avg_losses['g_loss']:.2f}")
        
        # 保存模型
        os.makedirs('demo_results', exist_ok=True)
        model.save_model('demo_results/wgan_gp_demo.pth')
        print("   ✅ 模型训练并保存完成")
        
        return model
    
    def generate_optimized_data(self, model):
        """生成优化的数据"""
        print("\n📊 步骤3: 生成优化数据")
        
        # 生成新数据
        num_samples = 1391
        print(f"   🎲 生成 {num_samples} 个样本...")
        generated_samples = model.generate_samples(num_samples)
        
        # 反标准化
        with open('processed_data/normalization_params.pkl', 'rb') as f:
            norm_params = pickle.load(f)
        
        denormalized = generated_samples.copy()
        mask = generated_samples != -100
        denormalized[mask] = (generated_samples[mask] * norm_params['std'] + norm_params['mean'])
        denormalized[~mask] = -100
        denormalized[denormalized > 0] = -100
        
        # 优化策略：将大部分-89替换为100
        minus_89_mask = np.abs(denormalized + 89) < 1.0
        minus_89_positions = np.where(minus_89_mask)
        
        if len(minus_89_positions[0]) > 0:
            # 随机选择80%的-89值替换为100
            n_to_replace = int(len(minus_89_positions[0]) * 0.8)
            if n_to_replace > 0:
                indices = np.random.choice(len(minus_89_positions[0]), n_to_replace, replace=False)
                rows = minus_89_positions[0][indices]
                cols = minus_89_positions[1][indices]
                denormalized[rows, cols] = 100
                print(f"   🔄 将 {n_to_replace} 个-89值替换为100")
        
        return denormalized
    
    def create_final_dataset(self, generated_data):
        """创建最终数据集"""
        print("\n🔗 步骤4: 创建最终数据集")
        
        # 加载原始数据
        original_data = pd.read_csv('processed_data/building0_floor3_processed.csv')
        
        # 创建生成数据的DataFrame
        wap_cols = [f'WAP{i+1:03d}' for i in range(520)]
        generated_df = pd.DataFrame(generated_data, columns=wap_cols)
        
        # 添加位置信息
        position_cols = ['LONGITUDE', 'LATITUDE']
        position_indices = np.random.choice(len(original_data), len(generated_df), replace=True)
        position_data = original_data[position_cols].iloc[position_indices].reset_index(drop=True)
        
        # 合并生成数据
        final_generated_data = pd.concat([generated_df, position_data], axis=1)
        
        # 合并原始数据和生成数据
        wap_cols_orig = [col for col in original_data.columns if col.startswith('WAP')]
        original_reordered = original_data[wap_cols_orig + position_cols]
        final_generated_reordered = final_generated_data[wap_cols + position_cols]
        
        # 合并数据
        merged_data = pd.concat([original_reordered, final_generated_reordered], ignore_index=True)
        
        # 最终处理
        for col in wap_cols:
            merged_data.loc[(merged_data[col] > 0) & (merged_data[col] != 100), col] = -100
        
        print(f"   ✅ 最终数据集形状: {merged_data.shape}")
        print(f"   📊 原始数据: {len(original_data)} 条")
        print(f"   📊 生成数据: {len(final_generated_data)} 条")
        print(f"   📊 合并后数据: {len(merged_data)} 条")
        
        return merged_data
    
    def evaluate_quality(self, final_dataset):
        """评估数据质量"""
        print("\n📈 步骤5: 评估数据质量")
        
        mid_point = len(final_dataset) // 2
        wap_cols = [col for col in final_dataset.columns if col.startswith('WAP')]
        
        original_wap = final_dataset.iloc[:mid_point][wap_cols].values
        generated_wap = final_dataset.iloc[mid_point:][wap_cols].values
        
        print("   🔍 计算评估指标...")
        results = self.evaluator.evaluate_all(original_wap, generated_wap)
        
        # 检查质量
        kl_ok = results.get('kl_divergence', 10) < 0.2
        js_ok = results.get('js_divergence', 10) < 0.2
        wd_ok = results.get('wasserstein_distance', 100) < 2.0
        
        print(f"   📊 KL散度: {results.get('kl_divergence', 0):.4f} {'✅' if kl_ok else '❌'}")
        print(f"   📊 JS散度: {results.get('js_divergence', 0):.4f} {'✅' if js_ok else '❌'}")
        print(f"   📊 Wasserstein距离: {results.get('wasserstein_distance', 0):.4f} {'✅' if wd_ok else '❌'}")
        
        targets_met = sum([kl_ok, js_ok, wd_ok])
        print(f"   🎯 目标达成: {targets_met}/3 项指标")
        
        return results
    
    def save_results(self, final_dataset, results):
        """保存结果"""
        print("\n💾 步骤6: 保存结果")
        
        # 创建结果目录
        os.makedirs('final_project_results', exist_ok=True)
        
        # 保存最终数据集
        csv_path = 'final_project_results/final_wifi_dataset.csv'
        final_dataset.to_csv(csv_path, index=False)
        print(f"   ✅ CSV文件: {csv_path}")
        
        mat_path = 'final_project_results/final_wifi_dataset.mat'
        mat_data = {
            'data': final_dataset.values,
            'columns': final_dataset.columns.tolist(),
            'shape': final_dataset.shape
        }
        savemat(mat_path, mat_data)
        print(f"   ✅ MAT文件: {mat_path}")
        
        # 创建简单的结果图表
        self.create_results_chart(results)
        
        return csv_path, mat_path
    
    def create_results_chart(self, results):
        """创建结果图表"""
        print("   📊 生成结果图表...")
        
        fig, ax = plt.subplots(1, 1, figsize=(10, 6))
        
        # 准备数据
        metrics = ['KL散度', 'JS散度', 'Wasserstein距离']
        values = [
            results.get('kl_divergence', 0),
            results.get('js_divergence', 0),
            results.get('wasserstein_distance', 0)
        ]
        targets = [0.2, 0.2, 2.0]
        
        # 创建条形图
        x = np.arange(len(metrics))
        width = 0.35
        
        bars1 = ax.bar(x - width/2, values, width, label='实际值', alpha=0.8, color='#3498db')
        bars2 = ax.bar(x + width/2, targets, width, label='目标值', alpha=0.8, color='#e74c3c')
        
        ax.set_title('WiFi指纹数据集质量评估结果', fontsize=14, fontweight='bold')
        ax.set_xlabel('评估指标')
        ax.set_ylabel('数值')
        ax.set_xticks(x)
        ax.set_xticklabels(metrics)
        ax.legend()
        ax.grid(True, alpha=0.3, axis='y')
        
        # 添加数值标签
        for bars in [bars1, bars2]:
            for bar in bars:
                height = bar.get_height()
                ax.annotate(f'{height:.3f}',
                           xy=(bar.get_x() + bar.get_width() / 2, height),
                           xytext=(0, 3),
                           textcoords="offset points",
                           ha='center', va='bottom', fontsize=10)
        
        plt.tight_layout()
        plt.savefig('final_project_results/evaluation_results.png', dpi=300, bbox_inches='tight')
        print(f"   ✅ 图表文件: final_project_results/evaluation_results.png")
        plt.close()
    
    def run_complete_project(self):
        """运行完整项目"""
        try:
            # 1. 检查环境
            self.check_environment()
            
            # 2. 准备模型
            model = self.load_existing_model_or_train()
            
            # 3. 生成优化数据
            generated_data = self.generate_optimized_data(model)
            
            # 4. 创建最终数据集
            final_dataset = self.create_final_dataset(generated_data)
            
            # 5. 评估质量
            results = self.evaluate_quality(final_dataset)
            
            # 6. 保存结果
            csv_path, mat_path = self.save_results(final_dataset, results)
            
            # 7. 项目总结
            self.print_project_summary(final_dataset, results)
            
            return True
            
        except Exception as e:
            print(f"\n❌ 项目运行出错: {e}")
            return False
    
    def print_project_summary(self, final_dataset, results):
        """打印项目总结"""
        print("\n" + "=" * 80)
        print("🎉 项目运行完成总结")
        print("=" * 80)
        
        print(f"完成时间: {datetime.now()}")
        print(f"最终数据集形状: {final_dataset.shape}")
        
        wap_cols = [col for col in final_dataset.columns if col.startswith('WAP')]
        wap_data = final_dataset[wap_cols]
        hundred_count = (wap_data == 100).sum().sum()
        total_count = wap_data.size
        
        print(f"\n📊 数据集统计:")
        print(f"   WAP特征数: {len(wap_cols)}")
        print(f"   位置特征数: 2")
        print(f"   总记录数: {len(final_dataset)}")
        print(f"   100值比例: {hundred_count/total_count*100:.2f}%")
        
        print(f"\n📈 质量评估:")
        print(f"   KL散度: {results.get('kl_divergence', 0):.4f}")
        print(f"   JS散度: {results.get('js_divergence', 0):.4f}")
        print(f"   Wasserstein距离: {results.get('wasserstein_distance', 0):.4f}")
        
        print(f"\n💾 输出文件:")
        print(f"   ✅ final_project_results/final_wifi_dataset.csv")
        print(f"   ✅ final_project_results/final_wifi_dataset.mat")
        print(f"   ✅ final_project_results/evaluation_results.png")
        
        print("\n🎯 项目成果:")
        print("   ✅ 成功扩充WiFi指纹数据集")
        print("   ✅ 实现-89到100的大规模替换")
        print("   ✅ 生成完整的评估报告")
        print("   ✅ 提供多格式数据输出")
        
        print("=" * 80)

def main():
    """主函数"""
    runner = CompleteProjectRunner()
    success = runner.run_complete_project()
    
    if success:
        print("\n🎉 项目运行成功！")
    else:
        print("\n❌ 项目运行失败！")
    
    return success

if __name__ == "__main__":
    main()
