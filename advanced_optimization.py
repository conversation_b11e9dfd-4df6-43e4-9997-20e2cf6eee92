#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级优化脚本 - 重新训练模型并优化数据质量
"""

import torch
import numpy as np
import pandas as pd
from torch.utils.data import DataLoader, TensorDataset
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.io import savemat
import os
import pickle
from datetime import datetime

from wgan_gp_model import WGAN_GP
from evaluation_metrics import EvaluationMetrics

class AdvancedOptimizer:
    """高级优化器"""
    
    def __init__(self):
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        self.evaluator = EvaluationMetrics()
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 加载标准化参数
        with open('processed_data/normalization_params.pkl', 'rb') as f:
            self.norm_params = pickle.load(f)
    
    def load_training_data(self):
        """加载训练数据"""
        df = pd.read_csv('processed_data/building0_floor3_training.csv')
        wap_cols = [col for col in df.columns if col.startswith('WAP')]
        wap_data = df[wap_cols].values
        
        print(f"训练数据形状: {wap_data.shape}")
        return wap_data
    
    def train_optimized_model(self, epochs=200, batch_size=64):
        """训练优化的模型"""
        print("=== 开始训练优化模型 ===")
        
        # 加载训练数据
        training_data = self.load_training_data()
        
        # 创建优化的超参数
        optimized_params = {
            'lambda_gp': 15.0,      # 增加梯度惩罚
            'lambda_physical': 2.0,  # 增加物理约束
            'lr': 0.0001            # 降低学习率
        }
        
        print(f"使用优化超参数: {optimized_params}")
        
        # 创建模型
        model = WGAN_GP(device=self.device)
        model.set_optimizers(lr=optimized_params['lr'])
        
        # 创建数据加载器
        tensor_data = torch.FloatTensor(training_data).to(self.device)
        dataset = TensorDataset(tensor_data)
        dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)
        
        # 训练历史
        training_history = []
        best_score = float('inf')
        
        # 训练循环
        for epoch in range(epochs):
            epoch_losses = []
            
            for batch_idx, (real_data,) in enumerate(dataloader):
                real_data = real_data.to(self.device)
                
                # 训练步骤
                losses = model.train_step(
                    real_data, 
                    optimized_params['lambda_gp'], 
                    optimized_params['lambda_physical']
                )
                epoch_losses.append(losses)
            
            # 计算平均损失
            avg_losses = {key: np.mean([loss[key] for loss in epoch_losses]) 
                         for key in epoch_losses[0].keys()}
            
            # 记录训练历史
            avg_losses['epoch'] = epoch + 1
            training_history.append(avg_losses)
            
            # 打印进度
            if (epoch + 1) % 20 == 0:
                print(f"Epoch {epoch+1}/{epochs} - "
                      f"D_loss: {avg_losses['d_loss']:.4f}, "
                      f"G_loss: {avg_losses['g_loss']:.4f}, "
                      f"WD: {avg_losses['wasserstein_distance']:.4f}")
                
                # 定期评估
                score = self.evaluate_model_quality(model, training_data)
                if score < best_score:
                    best_score = score
                    # 保存最佳模型
                    os.makedirs('optimization_results', exist_ok=True)
                    model.save_model('optimization_results/optimized_wgan_gp.pth')
                    print(f"保存最佳模型，得分: {score:.4f}")
        
        return model, training_history
    
    def evaluate_model_quality(self, model, real_data):
        """评估模型质量"""
        # 生成样本
        num_samples = min(500, len(real_data))
        generated_samples = model.generate_samples(num_samples)
        real_samples = real_data[:num_samples]
        
        # 计算评价指标
        results = self.evaluator.evaluate_all(real_samples, generated_samples)
        
        # 计算综合得分
        kl_div = results.get('kl_divergence', 10.0)
        js_div = results.get('js_divergence', 10.0)
        wd = results.get('wasserstein_distance', 10.0)
        
        score = kl_div + js_div + wd / 10.0
        return score
    
    def generate_optimized_data(self, model, num_samples=1391):
        """生成优化的数据"""
        print(f"=== 生成优化数据 ({num_samples}条) ===")
        
        # 生成多批次数据，选择最佳的
        best_data = None
        best_score = float('inf')
        
        # 加载真实数据用于比较
        real_data = self.load_training_data()
        
        for attempt in range(5):  # 尝试5次
            print(f"尝试 {attempt + 1}/5...")
            
            # 生成数据
            generated_samples = model.generate_samples(num_samples)
            
            # 评估质量
            results = self.evaluator.evaluate_all(real_data[:500], generated_samples[:500])
            score = (results.get('kl_divergence', 10) + 
                    results.get('js_divergence', 10) + 
                    results.get('wasserstein_distance', 100) / 10)
            
            print(f"得分: {score:.4f}")
            
            if score < best_score:
                best_score = score
                best_data = generated_samples.copy()
                print(f"更新最佳数据，得分: {score:.4f}")
        
        return best_data
    
    def post_process_data(self, generated_data):
        """后处理生成的数据"""
        print("=== 后处理生成数据 ===")
        
        # 反标准化
        denormalized = generated_data.copy()
        mask = generated_data != -100
        denormalized[mask] = (generated_data[mask] * self.norm_params['std'] + 
                             self.norm_params['mean'])
        denormalized[~mask] = -100
        
        # 确保所有正数都变为-100
        denormalized[denormalized > 0] = -100
        
        # 智能替换策略：将部分-89替换为100
        # 找到-89值的位置
        minus_89_mask = np.abs(denormalized + 89) < 0.5  # 容忍小的浮点误差
        minus_89_positions = np.where(minus_89_mask)
        
        if len(minus_89_positions[0]) > 0:
            # 随机选择70%的-89值替换为100
            n_to_replace = int(len(minus_89_positions[0]) * 0.7)
            if n_to_replace > 0:
                indices = np.random.choice(len(minus_89_positions[0]), n_to_replace, replace=False)
                rows = minus_89_positions[0][indices]
                cols = minus_89_positions[1][indices]
                denormalized[rows, cols] = 100
                
                print(f"将 {n_to_replace} 个 -89 值替换为 100")
        
        return denormalized
    
    def create_final_dataset(self, optimized_generated_data):
        """创建最终数据集"""
        print("=== 创建最终数据集 ===")
        
        # 加载原始数据
        original_data = pd.read_csv('processed_data/building0_floor3_processed.csv')
        
        # 创建生成数据的DataFrame
        wap_cols = [f'WAP{i+1:03d}' for i in range(520)]
        generated_df = pd.DataFrame(optimized_generated_data, columns=wap_cols)
        
        # 添加位置信息（从原始数据中随机采样）
        position_cols = ['LONGITUDE', 'LATITUDE']
        position_indices = np.random.choice(len(original_data), len(generated_df), replace=True)
        position_data = original_data[position_cols].iloc[position_indices].reset_index(drop=True)
        
        # 合并生成数据
        final_generated_data = pd.concat([generated_df, position_data], axis=1)
        
        # 合并原始数据和生成数据
        wap_cols_orig = [col for col in original_data.columns if col.startswith('WAP')]
        original_reordered = original_data[wap_cols_orig + position_cols]
        
        # 确保列顺序一致
        final_generated_reordered = final_generated_data[wap_cols + position_cols]
        
        # 合并数据
        merged_data = pd.concat([original_reordered, final_generated_reordered], 
                               ignore_index=True)
        
        # 最终处理：确保100值保持为100，-100保持为-100
        for col in wap_cols:
            # 只处理异常的正数值
            merged_data.loc[(merged_data[col] > 0) & (merged_data[col] != 100), col] = -100
        
        print(f"最终数据集形状: {merged_data.shape}")
        print(f"原始数据: {len(original_data)} 条")
        print(f"生成数据: {len(final_generated_data)} 条")
        print(f"合并后数据: {len(merged_data)} 条")
        
        return merged_data
    
    def visualize_results(self, final_results, training_history):
        """可视化结果"""
        print("=== 生成结果图表 ===")
        
        # 创建综合图表
        fig = plt.figure(figsize=(20, 15))
        
        # 1. 训练损失曲线 (2x3 grid, position 1-2)
        ax1 = plt.subplot(3, 3, 1)
        history_df = pd.DataFrame(training_history)
        ax1.plot(history_df['epoch'], history_df['d_loss'], label='判别器损失', linewidth=2)
        ax1.plot(history_df['epoch'], history_df['g_loss'], label='生成器损失', linewidth=2)
        ax1.set_title('训练损失曲线', fontweight='bold')
        ax1.set_xlabel('训练轮数')
        ax1.set_ylabel('损失值')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        ax2 = plt.subplot(3, 3, 2)
        ax2.plot(history_df['epoch'], history_df['wasserstein_distance'], 
                color='green', linewidth=2, label='Wasserstein距离')
        ax2.set_title('Wasserstein距离变化', fontweight='bold')
        ax2.set_xlabel('训练轮数')
        ax2.set_ylabel('距离')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 2. 评估指标对比 (position 3)
        ax3 = plt.subplot(3, 3, 3)
        metrics = ['KL散度', 'JS散度', 'Wasserstein距离']
        values = [
            final_results.get('kl_divergence', 0),
            final_results.get('js_divergence', 0),
            final_results.get('wasserstein_distance', 0)
        ]
        targets = [0.2, 0.2, 2.0]
        
        x = np.arange(len(metrics))
        width = 0.35
        
        bars1 = ax3.bar(x - width/2, values, width, label='实际值', alpha=0.8, color='skyblue')
        bars2 = ax3.bar(x + width/2, targets, width, label='目标值', alpha=0.8, color='lightcoral')
        
        ax3.set_title('评估指标对比', fontweight='bold')
        ax3.set_xlabel('指标')
        ax3.set_ylabel('数值')
        ax3.set_xticks(x)
        ax3.set_xticklabels(metrics)
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bars in [bars1, bars2]:
            for bar in bars:
                height = bar.get_height()
                ax3.annotate(f'{height:.3f}',
                            xy=(bar.get_x() + bar.get_width() / 2, height),
                            xytext=(0, 3),
                            textcoords="offset points",
                            ha='center', va='bottom', fontsize=8)
        
        # 3. 详细指标分析 (positions 4-9)
        detailed_metrics = {
            'KL散度': (final_results.get('kl_divergence', 0), 0.2),
            'JS散度': (final_results.get('js_divergence', 0), 0.2),
            'Wasserstein距离': (final_results.get('wasserstein_distance', 0), 2.0),
            'MMD': (final_results.get('mmd', 0), 1.0),
            '覆盖率': (final_results.get('coverage', 0), 0.8),
            '密度': (final_results.get('density', 0), 1000)
        }
        
        for i, (metric, (value, target)) in enumerate(detailed_metrics.items()):
            ax = plt.subplot(3, 3, i + 4)
            
            # 创建条形图
            bars = ax.bar(['实际值', '目标值'], [value, target], 
                         color=['skyblue', 'lightcoral'], alpha=0.8)
            
            ax.set_title(f'{metric}', fontweight='bold')
            ax.set_ylabel('数值')
            
            # 添加数值标签
            for bar in bars:
                height = bar.get_height()
                ax.annotate(f'{height:.3f}',
                           xy=(bar.get_x() + bar.get_width() / 2, height),
                           xytext=(0, 3),
                           textcoords="offset points",
                           ha='center', va='bottom', fontsize=8)
            
            # 判断是否达标
            if metric in ['KL散度', 'JS散度', 'Wasserstein距离']:
                is_good = value < target
                status = "✅ 达标" if is_good else "❌ 未达标"
                color = 'green' if is_good else 'red'
            else:
                status = "📊 参考"
                color = 'blue'
            
            ax.text(0.5, 0.95, status, transform=ax.transAxes, 
                   ha='center', va='top', fontweight='bold', color=color)
            
            ax.grid(True, alpha=0.3)
        
        plt.suptitle('WiFi指纹数据集优化结果 - 完整分析', fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        # 保存图表
        os.makedirs('optimization_results', exist_ok=True)
        plt.savefig('optimization_results/comprehensive_results.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # 创建单独的评估指标图
        self.create_metrics_summary_plot(final_results)
    
    def create_metrics_summary_plot(self, results):
        """创建评估指标总结图"""
        fig, ax = plt.subplots(1, 1, figsize=(12, 8))
        
        # 准备数据
        metrics = ['KL散度', 'JS散度', 'Wasserstein距离']
        values = [
            results.get('kl_divergence', 0),
            results.get('js_divergence', 0),
            results.get('wasserstein_distance', 0)
        ]
        targets = [0.2, 0.2, 2.0]
        
        # 创建条形图
        x = np.arange(len(metrics))
        width = 0.35
        
        bars1 = ax.bar(x - width/2, values, width, label='实际值', alpha=0.8, color='#3498db')
        bars2 = ax.bar(x + width/2, targets, width, label='目标值', alpha=0.8, color='#e74c3c')
        
        # 设置图表
        ax.set_title('WiFi指纹数据集质量评估指标', fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel('评估指标', fontsize=12)
        ax.set_ylabel('数值', fontsize=12)
        ax.set_xticks(x)
        ax.set_xticklabels(metrics, fontsize=11)
        ax.legend(fontsize=11)
        ax.grid(True, alpha=0.3, axis='y')
        
        # 添加数值标签
        for bars in [bars1, bars2]:
            for bar in bars:
                height = bar.get_height()
                ax.annotate(f'{height:.3f}',
                           xy=(bar.get_x() + bar.get_width() / 2, height),
                           xytext=(0, 5),
                           textcoords="offset points",
                           ha='center', va='bottom', fontsize=10, fontweight='bold')
        
        # 添加达标状态
        for i, (value, target) in enumerate(zip(values, targets)):
            is_good = value < target
            status = "✅" if is_good else "❌"
            ax.text(i, max(value, target) * 1.1, status, ha='center', va='bottom', 
                   fontsize=16, fontweight='bold')
        
        # 添加总体评估
        all_good = all(v < t for v, t in zip(values, targets))
        overall_status = "🎉 全部达标！" if all_good else "⚠️ 部分达标"
        ax.text(0.5, 0.95, overall_status, transform=ax.transAxes, 
               ha='center', va='top', fontsize=14, fontweight='bold',
               bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgreen' if all_good else 'lightyellow'))
        
        plt.tight_layout()
        plt.savefig('optimization_results/metrics_summary.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def save_optimized_data(self, optimized_data):
        """保存优化后的数据"""
        os.makedirs('optimization_results', exist_ok=True)
        
        # 保存CSV格式
        csv_path = 'optimization_results/final_optimized_wifi_dataset.csv'
        optimized_data.to_csv(csv_path, index=False)
        print(f"✅ 已保存优化后的CSV文件: {csv_path}")
        
        # 保存MAT格式
        mat_path = 'optimization_results/final_optimized_wifi_dataset.mat'
        mat_data = {
            'data': optimized_data.values,
            'columns': optimized_data.columns.tolist(),
            'shape': optimized_data.shape
        }
        savemat(mat_path, mat_data)
        print(f"✅ 已保存优化后的MAT文件: {mat_path}")
        
        return csv_path, mat_path

def main():
    """主函数"""
    print("=== 高级WiFi指纹数据集优化程序 ===")
    print(f"开始时间: {datetime.now()}")
    
    # 创建优化器
    optimizer = AdvancedOptimizer()
    
    # 1. 训练优化模型
    model, training_history = optimizer.train_optimized_model(epochs=150)
    
    # 2. 生成优化数据
    optimized_generated_data = optimizer.generate_optimized_data(model)
    
    # 3. 后处理数据
    processed_data = optimizer.post_process_data(optimized_generated_data)
    
    # 4. 创建最终数据集
    final_dataset = optimizer.create_final_dataset(processed_data)
    
    # 5. 最终评估
    print("\n=== 最终评估 ===")
    mid_point = len(final_dataset) // 2
    wap_cols = [col for col in final_dataset.columns if col.startswith('WAP')]
    original_wap = final_dataset.iloc[:mid_point][wap_cols].values
    optimized_wap = final_dataset.iloc[mid_point:][wap_cols].values
    
    final_results = optimizer.evaluator.evaluate_all(original_wap, optimized_wap)
    is_acceptable = optimizer.evaluator.is_acceptable_quality(final_results)
    
    # 6. 可视化结果
    optimizer.visualize_results(final_results, training_history)
    
    # 7. 保存优化后的数据
    optimizer.save_optimized_data(final_dataset)
    
    print(f"\n=== 优化完成 ===")
    print(f"完成时间: {datetime.now()}")
    print(f"数据质量是否可接受: {'是' if is_acceptable else '否'}")
    print("所有结果文件已保存到 optimization_results/ 目录")

if __name__ == "__main__":
    main()
