#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
贝叶斯优化模块 - 优化WGAN-GP超参数
"""

import numpy as np
import torch
from torch.utils.data import DataLoader, TensorDataset
import pandas as pd
import pickle
import os
from datetime import datetime
import json

# 尝试导入scikit-optimize，如果失败则使用简单的网格搜索
try:
    from skopt import gp_minimize
    from skopt.space import Real
    from skopt.utils import use_named_args
    SKOPT_AVAILABLE = True
    print("使用scikit-optimize进行贝叶斯优化")
except ImportError:
    SKOPT_AVAILABLE = False
    print("scikit-optimize不可用，将使用网格搜索")

from wgan_gp_model import WGAN_GP, load_training_data
from evaluation_metrics import EvaluationMetrics

class HyperparameterOptimizer:
    """超参数优化器"""
    
    def __init__(self, data_path='processed_data/building0_floor3_training.csv'):
        self.data_path = data_path
        self.evaluator = EvaluationMetrics()
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        # 加载训练数据
        self.training_data = load_training_data(data_path)
        print(f"训练数据形状: {self.training_data.shape}")
        
        # 优化历史
        self.optimization_history = []
        
        # 定义搜索空间
        if SKOPT_AVAILABLE:
            self.search_space = [
                Real(1.0, 50.0, name='lambda_gp'),      # 梯度惩罚系数
                Real(0.1, 10.0, name='lambda_physical'), # 物理约束系数
                Real(1e-5, 1e-2, name='lr', prior='log-uniform')  # 学习率
            ]
        else:
            # 网格搜索参数
            self.param_grid = {
                'lambda_gp': [5.0, 10.0, 20.0],
                'lambda_physical': [0.5, 1.0, 2.0],
                'lr': [1e-4, 5e-4, 1e-3]
            }
    
    def objective_function(self, params):
        """目标函数 - 返回需要最小化的值"""
        if SKOPT_AVAILABLE:
            lambda_gp, lambda_physical, lr = params
        else:
            lambda_gp = params['lambda_gp']
            lambda_physical = params['lambda_physical']
            lr = params['lr']
        
        print(f"\n=== 测试参数组合 ===")
        print(f"lambda_gp: {lambda_gp:.4f}")
        print(f"lambda_physical: {lambda_physical:.4f}")
        print(f"lr: {lr:.6f}")
        
        try:
            # 训练模型
            score = self.train_and_evaluate(lambda_gp, lambda_physical, lr)
            
            # 记录历史
            result = {
                'lambda_gp': lambda_gp,
                'lambda_physical': lambda_physical,
                'lr': lr,
                'score': score,
                'timestamp': datetime.now().isoformat()
            }
            self.optimization_history.append(result)
            
            print(f"得分: {score:.4f}")
            return score
            
        except Exception as e:
            print(f"训练失败: {e}")
            return 10.0  # 返回一个很大的值表示失败
    
    def train_and_evaluate(self, lambda_gp, lambda_physical, lr, epochs=100, batch_size=64):
        """训练模型并评估"""
        # 创建模型
        model = WGAN_GP(device=self.device)
        model.set_optimizers(lr=lr)
        
        # 创建数据加载器
        tensor_data = torch.FloatTensor(self.training_data).to(self.device)
        dataset = TensorDataset(tensor_data)
        dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)
        
        # 训练模型
        print("开始训练...")
        for epoch in range(epochs):
            epoch_losses = []
            
            for batch_idx, (real_data,) in enumerate(dataloader):
                real_data = real_data.to(self.device)
                
                # 训练步骤
                losses = model.train_step(real_data, lambda_gp, lambda_physical)
                epoch_losses.append(losses)
                
                # 每20个batch打印一次
                if batch_idx % 20 == 0:
                    print(f"Epoch {epoch+1}/{epochs}, Batch {batch_idx}, "
                          f"D_loss: {losses['d_loss']:.4f}, G_loss: {losses['g_loss']:.4f}")
        
        # 生成样本进行评估
        print("生成样本进行评估...")
        num_samples = min(1000, len(self.training_data))
        generated_samples = model.generate_samples(num_samples)
        
        # 计算评价指标
        real_samples = self.training_data[:num_samples]
        results = self.evaluator.evaluate_all(real_samples, generated_samples)
        
        # 计算综合得分（越小越好）
        kl_div = results.get('kl_divergence', 10.0)
        js_div = results.get('js_divergence', 10.0)
        wd = results.get('wasserstein_distance', 10.0)
        
        # 综合得分：KL散度 + JS散度 + 标准化的Wasserstein距离
        score = kl_div + js_div + wd / 10.0
        
        return score
    
    def optimize_with_skopt(self, n_calls=20):
        """使用scikit-optimize进行贝叶斯优化"""
        print("开始贝叶斯优化...")
        
        @use_named_args(self.search_space)
        def objective(**params):
            return self.objective_function([params['lambda_gp'], params['lambda_physical'], params['lr']])
        
        # 执行优化
        result = gp_minimize(
            func=objective,
            dimensions=self.search_space,
            n_calls=n_calls,
            random_state=42,
            acq_func='EI'  # Expected Improvement
        )
        
        return result
    
    def optimize_with_grid_search(self):
        """使用网格搜索进行优化"""
        print("开始网格搜索...")
        
        best_score = float('inf')
        best_params = None
        
        # 生成所有参数组合
        import itertools
        param_combinations = list(itertools.product(
            self.param_grid['lambda_gp'],
            self.param_grid['lambda_physical'],
            self.param_grid['lr']
        ))
        
        print(f"总共需要测试 {len(param_combinations)} 个参数组合")
        
        for i, (lambda_gp, lambda_physical, lr) in enumerate(param_combinations):
            print(f"\n进度: {i+1}/{len(param_combinations)}")
            
            params = {
                'lambda_gp': lambda_gp,
                'lambda_physical': lambda_physical,
                'lr': lr
            }
            
            score = self.objective_function(params)
            
            if score < best_score:
                best_score = score
                best_params = params
                print(f"发现更好的参数组合！得分: {best_score:.4f}")
        
        return best_params, best_score
    
    def optimize(self, method='auto', n_calls=20):
        """执行超参数优化"""
        if method == 'auto':
            method = 'bayesian' if SKOPT_AVAILABLE else 'grid'
        
        print(f"使用 {method} 方法进行优化")
        
        if method == 'bayesian' and SKOPT_AVAILABLE:
            result = self.optimize_with_skopt(n_calls)
            best_params = {
                'lambda_gp': result.x[0],
                'lambda_physical': result.x[1],
                'lr': result.x[2]
            }
            best_score = result.fun
        else:
            best_params, best_score = self.optimize_with_grid_search()
        
        # 保存结果
        self.save_optimization_results(best_params, best_score)
        
        return best_params, best_score
    
    def save_optimization_results(self, best_params, best_score):
        """保存优化结果"""
        results = {
            'best_params': best_params,
            'best_score': best_score,
            'optimization_history': self.optimization_history,
            'timestamp': datetime.now().isoformat()
        }
        
        # 保存为JSON文件
        with open('optimization_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\n=== 优化结果 ===")
        print(f"最佳参数:")
        for key, value in best_params.items():
            print(f"  {key}: {value}")
        print(f"最佳得分: {best_score:.4f}")
        print(f"结果已保存到 optimization_results.json")

if __name__ == "__main__":
    # 执行超参数优化
    print("=== 开始超参数优化 ===")
    
    optimizer = HyperparameterOptimizer()
    
    # 执行优化（使用较少的调用次数进行测试）
    best_params, best_score = optimizer.optimize(method='auto', n_calls=5)
    
    print("\n优化完成！")
