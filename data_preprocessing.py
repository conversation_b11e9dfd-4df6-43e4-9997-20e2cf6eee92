#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据预处理模块 - 提取和预处理楼栋0楼层3的数据
"""

import pandas as pd
import numpy as np
import os
from scipy.io import savemat
import pickle

class DataPreprocessor:
    def __init__(self, data_path='UJIndoorLoc/trainingData.csv'):
        self.data_path = data_path
        self.building_id = 0
        self.floor_id = 3
        self.wap_cols = None
        self.position_cols = ['LONGITUDE', 'LATITUDE']

    def load_and_extract_data(self):
        """加载数据并提取指定楼栋楼层的数据"""
        print(f"正在加载数据: {self.data_path}")
        df = pd.read_csv(self.data_path)

        # 获取WAP列
        self.wap_cols = [col for col in df.columns if col.startswith('WAP')]
        print(f"WAP列数: {len(self.wap_cols)}")

        # 提取楼栋0楼层3的数据
        target_data = df[(df['BUILDINGID'] == self.building_id) &
                        (df['FLOOR'] == self.floor_id)].copy()

        print(f"提取到楼栋{self.building_id}楼层{self.floor_id}的数据: {len(target_data)}条")

        if len(target_data) == 0:
            raise ValueError(f"没有找到楼栋{self.building_id}楼层{self.floor_id}的数据")

        return target_data

    def preprocess_data(self, data):
        """预处理数据"""
        print("开始数据预处理...")

        # 提取WAP数据和位置数据
        wap_data = data[self.wap_cols].copy()
        position_data = data[self.position_cols].copy()

        # 保存原始数据统计信息
        original_stats = {
            'shape': wap_data.shape,
            'non_100_count': (wap_data != 100).sum().sum(),
            'total_count': wap_data.size,
            'signal_range': {
                'min': wap_data[wap_data != 100].min().min(),
                'max': wap_data[wap_data != 100].max().max(),
                'mean': wap_data[wap_data != 100].mean().mean(),
                'std': wap_data[wap_data != 100].std().mean()
            }
        }

        print(f"原始数据统计: {original_stats}")

        # 将100值替换为-100（表示无信号）
        wap_data_processed = wap_data.replace(100, -100)

        # 检查是否有正数值，如果有也替换为-100
        positive_mask = wap_data_processed > 0
        if positive_mask.any().any():
            positive_count = positive_mask.sum().sum()
            print(f"发现{positive_count}个正数值，将替换为-100")
            wap_data_processed[positive_mask] = -100

        # 合并WAP数据和位置数据
        processed_data = pd.concat([wap_data_processed, position_data], axis=1)

        return processed_data, original_stats

    def save_data(self, data, filename_prefix, save_dir='processed_data'):
        """保存数据为CSV和MAT格式"""
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)

        # 保存CSV格式
        csv_path = os.path.join(save_dir, f"{filename_prefix}.csv")
        data.to_csv(csv_path, index=False)
        print(f"已保存CSV文件: {csv_path}")

        # 保存MAT格式
        mat_path = os.path.join(save_dir, f"{filename_prefix}.mat")
        mat_data = {
            'data': data.values,
            'columns': data.columns.tolist(),
            'shape': data.shape
        }
        savemat(mat_path, mat_data)
        print(f"已保存MAT文件: {mat_path}")

        return csv_path, mat_path

    def normalize_for_training(self, wap_data):
        """标准化WAP数据用于训练"""
        # 只对非-100的值进行标准化
        mask = wap_data != -100

        # 计算统计信息（只考虑有效信号值）
        valid_values = wap_data[mask].values.flatten()
        valid_values = valid_values[~np.isnan(valid_values)]

        if len(valid_values) == 0:
            raise ValueError("没有有效的信号值用于标准化")

        mean_val = np.mean(valid_values)
        std_val = np.std(valid_values)

        print(f"标准化参数 - 均值: {mean_val:.2f}, 标准差: {std_val:.2f}")

        # 标准化
        normalized_data = wap_data.copy()
        normalized_data[mask] = (wap_data[mask] - mean_val) / std_val

        # 保存标准化参数
        norm_params = {
            'mean': float(mean_val),
            'std': float(std_val),
            'mask_value': -100
        }

        return normalized_data, norm_params

    def process_building0_floor3(self):
        """完整处理楼栋0楼层3的数据"""
        print("=== 开始处理楼栋0楼层3数据 ===")

        # 1. 加载和提取数据
        raw_data = self.load_and_extract_data()

        # 2. 预处理数据
        processed_data, stats = self.preprocess_data(raw_data)

        # 3. 保存原始提取的数据
        self.save_data(raw_data, "building0_floor3_original")

        # 4. 保存预处理后的数据
        self.save_data(processed_data, "building0_floor3_processed")

        # 5. 准备训练数据
        wap_data = processed_data[self.wap_cols]
        position_data = processed_data[self.position_cols]

        # 6. 标准化WAP数据
        normalized_wap, norm_params = self.normalize_for_training(wap_data)

        # 7. 保存标准化参数
        with open('processed_data/normalization_params.pkl', 'wb') as f:
            pickle.dump(norm_params, f)
        print("已保存标准化参数")

        # 8. 保存训练用数据
        training_data = pd.concat([normalized_wap, position_data], axis=1)
        self.save_data(training_data, "building0_floor3_training")

        print("=== 数据预处理完成 ===")
        print(f"原始数据: {len(raw_data)}条")
        print(f"处理后数据: {len(processed_data)}条")
        print(f"WAP特征数: {len(self.wap_cols)}")
        print(f"位置特征数: {len(self.position_cols)}")

        return {
            'raw_data': raw_data,
            'processed_data': processed_data,
            'training_data': training_data,
            'wap_cols': self.wap_cols,
            'position_cols': self.position_cols,
            'norm_params': norm_params,
            'stats': stats
        }

if __name__ == "__main__":
    preprocessor = DataPreprocessor()
    result = preprocessor.process_building0_floor3()
