#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据分析脚本 - 分析UJIndoorLoc数据集
"""

import pandas as pd
import numpy as np
import os

def analyze_data():
    """分析数据集基本信息"""
    print("=== UJIndoorLoc数据集分析 ===")

    # 读取训练数据
    train_file = 'UJIndoorLoc/trainingData.csv'
    if not os.path.exists(train_file):
        print(f"错误：找不到文件 {train_file}")
        return

    df = pd.read_csv(train_file)
    print(f"训练数据形状: {df.shape}")
    print(f"列数: {len(df.columns)}")

    # 显示列名信息
    wap_cols = [col for col in df.columns if col.startswith('WAP')]
    other_cols = [col for col in df.columns if not col.startswith('WAP')]

    print(f"WAP列数: {len(wap_cols)}")
    print(f"其他列: {other_cols}")

    # 分析楼栋和楼层信息
    print("\n=== 楼栋和楼层分布 ===")
    building_floor_counts = df[['BUILDINGID', 'FLOOR']].value_counts().sort_index()
    print(building_floor_counts)

    # 分析楼栋0楼层3的数据
    print("\n=== 楼栋0楼层3数据分析 ===")
    building0_floor3 = df[(df['BUILDINGID'] == 0) & (df['FLOOR'] == 3)]
    print(f"楼栋0楼层3数据量: {len(building0_floor3)}")

    if len(building0_floor3) > 0:
        # 分析WAP信号强度分布
        wap_data = building0_floor3[wap_cols]
        print(f"WAP数据形状: {wap_data.shape}")

        # 统计非100值的分布
        non_100_mask = wap_data != 100
        non_100_count = non_100_mask.sum().sum()
        total_count = wap_data.size
        print(f"非100值数量: {non_100_count} / {total_count} ({non_100_count/total_count*100:.2f}%)")

        # 统计信号强度范围
        non_100_values = wap_data[wap_data != 100].values.flatten()
        non_100_values = non_100_values[~np.isnan(non_100_values)]
        if len(non_100_values) > 0:
            print(f"信号强度范围: {non_100_values.min():.2f} 到 {non_100_values.max():.2f}")
            print(f"信号强度均值: {non_100_values.mean():.2f}")
            print(f"信号强度标准差: {non_100_values.std():.2f}")

        # 分析位置信息
        print(f"\n位置信息:")
        print(f"经度范围: {building0_floor3['LONGITUDE'].min():.2f} 到 {building0_floor3['LONGITUDE'].max():.2f}")
        print(f"纬度范围: {building0_floor3['LATITUDE'].min():.2f} 到 {building0_floor3['LATITUDE'].max():.2f}")

        return building0_floor3, wap_cols
    else:
        print("警告：楼栋0楼层3没有数据！")
        return None, wap_cols

if __name__ == "__main__":
    analyze_data()
