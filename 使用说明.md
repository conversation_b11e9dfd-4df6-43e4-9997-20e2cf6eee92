# WiFi指纹数据集扩充项目使用说明

## 快速开始

### 1. 环境准备
```bash
# 安装必要的Python包
pip install torch pandas numpy scipy scikit-learn matplotlib seaborn
```

### 2. 数据准备
确保UJIndoorLoc数据文件位于正确位置：
```
UJIndoorLoc/
└── trainingData.csv
```

### 3. 运行演示版本（推荐）
```bash
python demo_training.py
```
这将运行一个快速演示版本，大约需要5-10分钟完成。

## 详细使用流程

### 步骤1：数据预处理
```bash
python data_preprocessing.py
```

**功能**：
- 提取楼栋0楼层3的数据
- 将100值替换为-100（无信号标识）
- 将正数值替换为-100
- 数据标准化
- 保存为CSV和MAT格式

**输出文件**：
- `processed_data/building0_floor3_original.csv` - 原始提取数据
- `processed_data/building0_floor3_processed.csv` - 预处理后数据
- `processed_data/building0_floor3_training.csv` - 训练用数据
- `processed_data/normalization_params.pkl` - 标准化参数

### 步骤2：模型训练

#### 选项A：演示版本（推荐新手）
```bash
python demo_training.py
```
- 训练时间：5-10分钟
- 训练轮数：30 epochs
- 适合：快速验证流程

#### 选项B：完整版本
```bash
python final_training.py
```
- 训练时间：1-3小时
- 训练轮数：300 epochs
- 适合：获得最佳质量

### 步骤3：超参数优化（可选）
```bash
python bayesian_optimization.py
```
- 自动寻找最优超参数
- 时间较长，建议有经验用户使用

## 文件结构说明

### 输入文件
```
UJIndoorLoc/
└── trainingData.csv          # 原始UJIndoorLoc数据集
```

### 输出文件
```
processed_data/               # 预处理数据
├── building0_floor3_original.csv
├── building0_floor3_processed.csv
├── building0_floor3_training.csv
└── normalization_params.pkl

demo_results/                 # 演示版本结果
├── demo_generated_wifi_data.csv
├── demo_generated_wifi_data.mat
├── demo_merged_wifi_dataset.csv
├── demo_merged_wifi_dataset.mat
└── wgan_gp_demo.pth

final_results/                # 完整版本结果（如果运行）
├── generated_wifi_data.csv
├── generated_wifi_data.mat
├── merged_wifi_dataset.csv
├── merged_wifi_dataset.mat
└── training_history.png

models/                       # 训练好的模型
├── wgan_gp_best.pth
├── wgan_gp_final.pth
└── wgan_gp_epoch_*.pth
```

## 参数配置

### 修改目标楼层
如需处理其他楼层，修改`data_preprocessing.py`中的参数：
```python
self.building_id = 0  # 楼栋ID
self.floor_id = 3     # 楼层ID
```

### 调整训练参数
在训练脚本中修改：
```python
# 超参数
hyperparams = {
    'lambda_gp': 10.0,      # 梯度惩罚系数
    'lambda_physical': 1.0,  # 物理约束系数
    'lr': 0.0002            # 学习率
}

# 训练参数
epochs = 300        # 训练轮数
batch_size = 64     # 批次大小
```

### 调整网络架构
在`wgan_gp_model.py`中修改：
```python
# 生成器隐藏层
hidden_dims = [256, 512, 1024]

# 判别器隐藏层
hidden_dims = [1024, 512, 256]
```

## 数据格式说明

### CSV格式
- 第一行：列名（WAP001, WAP002, ..., WAP520, LONGITUDE, LATITUDE）
- 数据行：每行一个样本
- WAP值：-100表示无信号，其他负值表示信号强度（dBm）
- 位置：经度和纬度坐标

### MAT格式
包含以下字段：
- `data`: 数据矩阵
- `columns`: 列名列表
- `shape`: 数据形状

## 质量评估

### 评价指标
- **KL散度**: < 0.2 为优秀
- **JS散度**: < 0.2 为优秀
- **Wasserstein距离**: < 2.0 为合格

### 查看评估结果
训练过程中会自动显示评价指标：
```
KL散度: 0.1234
JS散度: 0.0987
Wasserstein距离: 1.5678
质量评估: ✓ 可接受
```

## 常见问题

### Q1: 内存不足怎么办？
A1: 减小批次大小：
```python
batch_size = 32  # 或更小
```

### Q2: 训练速度太慢？
A2: 
- 使用GPU：确保安装CUDA版本的PyTorch
- 减少训练轮数：
```python
epochs = 100  # 减少轮数
```

### Q3: 生成质量不好？
A3: 
- 增加训练轮数
- 调整超参数
- 运行超参数优化

### Q4: 如何处理其他楼层？
A4: 修改数据预处理脚本中的楼栋和楼层ID，然后重新运行整个流程。

### Q5: 如何使用生成的数据？
A5: 生成的数据可以直接用于：
- WiFi定位算法训练
- 机器学习模型训练
- 数据分析和可视化

## 技术支持

### 日志查看
训练过程中的详细信息会显示在控制台，包括：
- 损失函数值
- 评价指标
- 训练进度

### 模型保存
- 最佳模型：`models/wgan_gp_best.pth`
- 最终模型：`models/wgan_gp_final.pth`
- 定期保存：`models/wgan_gp_epoch_*.pth`

### 结果验证
检查生成数据的基本统计信息：
```python
import pandas as pd
data = pd.read_csv('demo_results/demo_merged_wifi_dataset.csv')
print(f"数据形状: {data.shape}")
print(f"WAP列数: {len([col for col in data.columns if col.startswith('WAP')])}")
```

## 扩展应用

### 批量处理多个楼层
创建脚本循环处理所有楼层：
```python
buildings = [0, 1, 2]
floors = [0, 1, 2, 3, 4]

for building in buildings:
    for floor in floors:
        # 修改参数并运行处理流程
        process_building_floor(building, floor)
```

### 集成到现有项目
生成的数据可以直接集成到现有的WiFi定位项目中，作为训练数据的补充。

## 性能优化建议

1. **使用GPU**: 显著加速训练过程
2. **调整批次大小**: 根据内存情况优化
3. **早停策略**: 监控验证指标，避免过拟合
4. **数据并行**: 对于大规模数据，考虑使用数据并行训练

## 版本更新

当前版本特性：
- ✅ 完整的WGAN-GP实现
- ✅ 多种评价指标
- ✅ 自动超参数优化
- ✅ CSV和MAT格式输出
- ✅ 物理约束和梯度惩罚
- ✅ 详细的训练日志和可视化
