#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主训练脚本 - 完整的WGAN-GP训练和数据生成流程
"""

import torch
import numpy as np
import pandas as pd
from torch.utils.data import DataLoader, TensorDataset
import matplotlib.pyplot as plt
import seaborn as sns
import os
import json
import pickle
from datetime import datetime
from scipy.io import savemat

from wgan_gp_model import WGAN_GP, load_training_data
from evaluation_metrics import EvaluationMetrics
from bayesian_optimization import HyperparameterOptimizer

class WiFiGANTrainer:
    """WiFi指纹数据GAN训练器"""
    
    def __init__(self, data_path='processed_data/building0_floor3_training.csv'):
        self.data_path = data_path
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        self.evaluator = EvaluationMetrics()
        
        # 加载训练数据
        self.training_data = load_training_data(data_path)
        print(f"训练数据形状: {self.training_data.shape}")
        
        # 加载标准化参数
        with open('processed_data/normalization_params.pkl', 'rb') as f:
            self.norm_params = pickle.load(f)
        print(f"标准化参数: {self.norm_params}")
        
        # 训练历史
        self.training_history = []
        
    def load_best_hyperparameters(self):
        """加载最佳超参数"""
        if os.path.exists('optimization_results.json'):
            with open('optimization_results.json', 'r', encoding='utf-8') as f:
                results = json.load(f)
            return results['best_params']
        else:
            # 默认参数
            return {
                'lambda_gp': 10.0,
                'lambda_physical': 1.0,
                'lr': 0.0001
            }
    
    def train_model(self, epochs=1000, batch_size=64, save_interval=100, eval_interval=50):
        """训练模型"""
        print("=== 开始训练WGAN-GP模型 ===")
        
        # 加载最佳超参数
        best_params = self.load_best_hyperparameters()
        print(f"使用超参数: {best_params}")
        
        # 创建模型
        self.model = WGAN_GP(device=self.device)
        self.model.set_optimizers(lr=best_params['lr'])
        
        # 创建数据加载器
        tensor_data = torch.FloatTensor(self.training_data).to(self.device)
        dataset = TensorDataset(tensor_data)
        dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)
        
        # 训练循环
        for epoch in range(epochs):
            epoch_losses = []
            
            for batch_idx, (real_data,) in enumerate(dataloader):
                real_data = real_data.to(self.device)
                
                # 训练步骤
                losses = self.model.train_step(
                    real_data, 
                    best_params['lambda_gp'], 
                    best_params['lambda_physical']
                )
                epoch_losses.append(losses)
            
            # 计算平均损失
            avg_losses = {key: np.mean([loss[key] for loss in epoch_losses]) 
                         for key in epoch_losses[0].keys()}
            
            # 记录训练历史
            avg_losses['epoch'] = epoch + 1
            self.training_history.append(avg_losses)
            
            # 打印进度
            if (epoch + 1) % 10 == 0:
                print(f"Epoch {epoch+1}/{epochs} - "
                      f"D_loss: {avg_losses['d_loss']:.4f}, "
                      f"G_loss: {avg_losses['g_loss']:.4f}, "
                      f"WD: {avg_losses['wasserstein_distance']:.4f}")
            
            # 定期评估
            if (epoch + 1) % eval_interval == 0:
                self.evaluate_current_model()
            
            # 定期保存模型
            if (epoch + 1) % save_interval == 0:
                self.model.save_model(f'models/wgan_gp_epoch_{epoch+1}.pth')
        
        # 保存最终模型
        os.makedirs('models', exist_ok=True)
        self.model.save_model('models/wgan_gp_final.pth')
        print("训练完成！")
    
    def evaluate_current_model(self):
        """评估当前模型"""
        print("评估当前模型...")
        
        # 生成样本
        num_samples = min(1000, len(self.training_data))
        generated_samples = self.model.generate_samples(num_samples)
        real_samples = self.training_data[:num_samples]
        
        # 计算评价指标
        results = self.evaluator.evaluate_all(real_samples, generated_samples)
        
        # 检查质量
        is_acceptable = self.evaluator.is_acceptable_quality(results)
        
        return results, is_acceptable
    
    def generate_final_data(self, num_samples=1391):
        """生成最终的WiFi指纹数据"""
        print(f"=== 生成 {num_samples} 个WiFi指纹样本 ===")
        
        # 加载最终模型
        if hasattr(self, 'model'):
            model = self.model
        else:
            model = WGAN_GP(device=self.device)
            model.load_model('models/wgan_gp_final.pth')
        
        # 生成样本
        generated_samples = model.generate_samples(num_samples)
        
        # 反标准化
        generated_denorm = self.denormalize_data(generated_samples)
        
        # 创建DataFrame
        wap_cols = [f'WAP{i+1:03d}' for i in range(520)]
        generated_df = pd.DataFrame(generated_denorm, columns=wap_cols)
        
        # 添加位置信息（从原始数据中随机采样）
        original_data = pd.read_csv('processed_data/building0_floor3_processed.csv')
        position_cols = ['LONGITUDE', 'LATITUDE']
        
        # 随机选择位置
        position_indices = np.random.choice(len(original_data), num_samples, replace=True)
        position_data = original_data[position_cols].iloc[position_indices].reset_index(drop=True)
        
        # 合并数据
        final_generated_data = pd.concat([generated_df, position_data], axis=1)
        
        # 保存生成的数据
        self.save_generated_data(final_generated_data, 'generated_wifi_data')
        
        return final_generated_data
    
    def denormalize_data(self, normalized_data):
        """反标准化数据"""
        denormalized = normalized_data.copy()
        
        # 创建mask用于标识有效信号值
        mask = normalized_data != -100  # -100是我们设置的无信号标识
        
        # 反标准化有效值
        denormalized[mask] = (normalized_data[mask] * self.norm_params['std'] + 
                             self.norm_params['mean'])
        
        # 将无信号值设为-100
        denormalized[~mask] = -100
        
        # 确保所有正数都变为-100
        denormalized[denormalized > 0] = -100
        
        return denormalized
    
    def merge_with_original_data(self, generated_data):
        """将生成数据与原始数据合并"""
        print("=== 合并生成数据与原始数据 ===")
        
        # 加载原始处理后的数据
        original_data = pd.read_csv('processed_data/building0_floor3_processed.csv')
        
        # 确保列顺序一致
        wap_cols = [col for col in original_data.columns if col.startswith('WAP')]
        position_cols = ['LONGITUDE', 'LATITUDE']
        
        # 重新排列生成数据的列顺序
        generated_reordered = generated_data[wap_cols + position_cols]
        original_reordered = original_data[wap_cols + position_cols]
        
        # 合并数据
        merged_data = pd.concat([original_reordered, generated_reordered], 
                               ignore_index=True)
        
        print(f"原始数据: {len(original_data)} 条")
        print(f"生成数据: {len(generated_data)} 条")
        print(f"合并后数据: {len(merged_data)} 条")
        
        # 最终处理：确保100值变为-100，正数变为-100
        for col in wap_cols:
            merged_data[col] = merged_data[col].replace(100, -100)
            merged_data.loc[merged_data[col] > 0, col] = -100
        
        # 保存合并后的数据
        self.save_generated_data(merged_data, 'merged_wifi_dataset')
        
        return merged_data
    
    def save_generated_data(self, data, filename_prefix):
        """保存生成的数据为CSV和MAT格式"""
        os.makedirs('generated_data', exist_ok=True)
        
        # 保存CSV格式
        csv_path = f'generated_data/{filename_prefix}.csv'
        data.to_csv(csv_path, index=False)
        print(f"已保存CSV文件: {csv_path}")
        
        # 保存MAT格式
        mat_path = f'generated_data/{filename_prefix}.mat'
        mat_data = {
            'data': data.values,
            'columns': data.columns.tolist(),
            'shape': data.shape
        }
        savemat(mat_path, mat_data)
        print(f"已保存MAT文件: {mat_path}")
        
        return csv_path, mat_path
    
    def plot_training_history(self):
        """绘制训练历史"""
        if not self.training_history:
            print("没有训练历史数据")
            return
        
        history_df = pd.DataFrame(self.training_history)
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 损失曲线
        axes[0, 0].plot(history_df['epoch'], history_df['d_loss'], label='Discriminator Loss')
        axes[0, 0].plot(history_df['epoch'], history_df['g_loss'], label='Generator Loss')
        axes[0, 0].set_title('Training Losses')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Loss')
        axes[0, 0].legend()
        
        # Wasserstein距离
        axes[0, 1].plot(history_df['epoch'], history_df['wasserstein_distance'])
        axes[0, 1].set_title('Wasserstein Distance')
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('Distance')
        
        # 梯度惩罚
        axes[1, 0].plot(history_df['epoch'], history_df['gradient_penalty'])
        axes[1, 0].set_title('Gradient Penalty')
        axes[1, 0].set_xlabel('Epoch')
        axes[1, 0].set_ylabel('Penalty')
        
        # 物理约束损失
        axes[1, 1].plot(history_df['epoch'], history_df['physical_loss'])
        axes[1, 1].set_title('Physical Constraint Loss')
        axes[1, 1].set_xlabel('Epoch')
        axes[1, 1].set_ylabel('Loss')
        
        plt.tight_layout()
        plt.savefig('training_history.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("训练历史图已保存为 training_history.png")

def main():
    """主函数"""
    print("=== WiFi指纹数据集扩充项目 ===")
    
    # 创建训练器
    trainer = WiFiGANTrainer()
    
    # 1. 超参数优化（可选，如果已经有结果可以跳过）
    if not os.path.exists('optimization_results.json'):
        print("开始超参数优化...")
        optimizer = HyperparameterOptimizer()
        best_params, best_score = optimizer.optimize(method='auto', n_calls=3)
    
    # 2. 训练模型
    trainer.train_model(epochs=200, batch_size=64)
    
    # 3. 生成数据
    generated_data = trainer.generate_final_data(num_samples=1391)
    
    # 4. 合并数据
    merged_data = trainer.merge_with_original_data(generated_data)
    
    # 5. 最终评估
    print("\n=== 最终评估 ===")
    results, is_acceptable = trainer.evaluate_current_model()
    
    # 6. 绘制训练历史
    trainer.plot_training_history()
    
    print("\n=== 项目完成 ===")
    print(f"生成数据质量是否可接受: {'是' if is_acceptable else '否'}")
    print("所有数据文件已保存到 generated_data/ 目录")

if __name__ == "__main__":
    main()
