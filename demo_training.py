#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示训练脚本 - 快速演示完整流程
"""

import torch
import numpy as np
import pandas as pd
from torch.utils.data import DataLoader, TensorDataset
import matplotlib.pyplot as plt
import os
import pickle
from datetime import datetime
from scipy.io import savemat

from wgan_gp_model import WGAN_GP, load_training_data
from evaluation_metrics import EvaluationMetrics

class DemoWiFiGANTrainer:
    """演示版WiFi指纹数据GAN训练器"""
    
    def __init__(self, data_path='processed_data/building0_floor3_training.csv'):
        self.data_path = data_path
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        self.evaluator = EvaluationMetrics()
        
        # 加载训练数据
        self.training_data = load_training_data(data_path)
        print(f"训练数据形状: {self.training_data.shape}")
        
        # 加载标准化参数
        with open('processed_data/normalization_params.pkl', 'rb') as f:
            self.norm_params = pickle.load(f)
        print(f"标准化参数: {self.norm_params}")
        
        # 使用经验证的超参数
        self.hyperparams = {
            'lambda_gp': 10.0,      # 梯度惩罚系数
            'lambda_physical': 1.0,  # 物理约束系数
            'lr': 0.0002            # 学习率
        }
        
        # 训练历史
        self.training_history = []
        
    def train_model(self, epochs=50, batch_size=64):
        """训练模型（演示版本，较少epoch）"""
        print("=== 开始训练WGAN-GP模型（演示版本） ===")
        print(f"使用超参数: {self.hyperparams}")
        print(f"训练设备: {self.device}")
        
        # 创建模型
        self.model = WGAN_GP(device=self.device)
        self.model.set_optimizers(lr=self.hyperparams['lr'])
        
        # 创建数据加载器
        tensor_data = torch.FloatTensor(self.training_data).to(self.device)
        dataset = TensorDataset(tensor_data)
        dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)
        
        # 创建保存目录
        os.makedirs('demo_results', exist_ok=True)
        
        # 训练循环
        for epoch in range(epochs):
            epoch_losses = []
            
            for batch_idx, (real_data,) in enumerate(dataloader):
                real_data = real_data.to(self.device)
                
                # 训练步骤
                losses = self.model.train_step(
                    real_data, 
                    self.hyperparams['lambda_gp'], 
                    self.hyperparams['lambda_physical']
                )
                epoch_losses.append(losses)
                
                # 为了演示，只训练前10个batch
                if batch_idx >= 9:
                    break
            
            # 计算平均损失
            avg_losses = {key: np.mean([loss[key] for loss in epoch_losses]) 
                         for key in epoch_losses[0].keys()}
            
            # 记录训练历史
            avg_losses['epoch'] = epoch + 1
            self.training_history.append(avg_losses)
            
            # 打印进度
            if (epoch + 1) % 5 == 0:
                print(f"Epoch {epoch+1}/{epochs} - "
                      f"D_loss: {avg_losses['d_loss']:.4f}, "
                      f"G_loss: {avg_losses['g_loss']:.4f}, "
                      f"WD: {avg_losses['wasserstein_distance']:.4f}")
        
        # 保存模型
        self.model.save_model('demo_results/wgan_gp_demo.pth')
        print("演示训练完成！")
    
    def evaluate_current_model(self):
        """评估当前模型"""
        print("评估当前模型...")
        
        # 生成样本
        num_samples = 500  # 演示版本使用较少样本
        generated_samples = self.model.generate_samples(num_samples)
        real_samples = self.training_data[:num_samples]
        
        # 计算评价指标
        results = self.evaluator.evaluate_all(real_samples, generated_samples)
        
        # 检查质量
        is_acceptable = self.evaluator.is_acceptable_quality(results)
        
        # 计算综合得分
        kl_div = results.get('kl_divergence', 10.0)
        js_div = results.get('js_divergence', 10.0)
        wd = results.get('wasserstein_distance', 10.0)
        score = kl_div + js_div + wd / 10.0
        
        return score, is_acceptable, results
    
    def generate_final_data(self, num_samples=1391):
        """生成最终的WiFi指纹数据"""
        print(f"=== 生成 {num_samples} 个WiFi指纹样本 ===")
        
        # 生成样本
        generated_samples = self.model.generate_samples(num_samples)
        
        # 反标准化
        generated_denorm = self.denormalize_data(generated_samples)
        
        # 创建DataFrame
        wap_cols = [f'WAP{i+1:03d}' for i in range(520)]
        generated_df = pd.DataFrame(generated_denorm, columns=wap_cols)
        
        # 添加位置信息（从原始数据中随机采样）
        original_data = pd.read_csv('processed_data/building0_floor3_processed.csv')
        position_cols = ['LONGITUDE', 'LATITUDE']
        
        # 随机选择位置
        position_indices = np.random.choice(len(original_data), num_samples, replace=True)
        position_data = original_data[position_cols].iloc[position_indices].reset_index(drop=True)
        
        # 合并数据
        final_generated_data = pd.concat([generated_df, position_data], axis=1)
        
        # 保存生成的数据
        self.save_data(final_generated_data, 'demo_generated_wifi_data')
        
        return final_generated_data
    
    def denormalize_data(self, normalized_data):
        """反标准化数据"""
        denormalized = normalized_data.copy()
        
        # 创建mask用于标识有效信号值
        mask = normalized_data != -100  # -100是我们设置的无信号标识
        
        # 反标准化有效值
        denormalized[mask] = (normalized_data[mask] * self.norm_params['std'] + 
                             self.norm_params['mean'])
        
        # 将无信号值设为-100
        denormalized[~mask] = -100
        
        # 确保所有正数都变为-100
        denormalized[denormalized > 0] = -100
        
        return denormalized
    
    def merge_with_original_data(self, generated_data):
        """将生成数据与原始数据合并"""
        print("=== 合并生成数据与原始数据 ===")
        
        # 加载原始处理后的数据
        original_data = pd.read_csv('processed_data/building0_floor3_processed.csv')
        
        # 确保列顺序一致
        wap_cols = [col for col in original_data.columns if col.startswith('WAP')]
        position_cols = ['LONGITUDE', 'LATITUDE']
        
        # 重新排列生成数据的列顺序
        generated_reordered = generated_data[wap_cols + position_cols]
        original_reordered = original_data[wap_cols + position_cols]
        
        # 合并数据
        merged_data = pd.concat([original_reordered, generated_reordered], 
                               ignore_index=True)
        
        print(f"原始数据: {len(original_data)} 条")
        print(f"生成数据: {len(generated_data)} 条")
        print(f"合并后数据: {len(merged_data)} 条")
        
        # 最终处理：确保100值变为-100，正数变为-100
        for col in wap_cols:
            merged_data[col] = merged_data[col].replace(100, -100)
            merged_data.loc[merged_data[col] > 0, col] = -100
        
        # 保存合并后的数据
        self.save_data(merged_data, 'demo_merged_wifi_dataset')
        
        return merged_data
    
    def save_data(self, data, filename_prefix):
        """保存数据为CSV和MAT格式"""
        os.makedirs('demo_results', exist_ok=True)
        
        # 保存CSV格式
        csv_path = f'demo_results/{filename_prefix}.csv'
        data.to_csv(csv_path, index=False)
        print(f"已保存CSV文件: {csv_path}")
        
        # 保存MAT格式
        mat_path = f'demo_results/{filename_prefix}.mat'
        mat_data = {
            'data': data.values,
            'columns': data.columns.tolist(),
            'shape': data.shape
        }
        savemat(mat_path, mat_data)
        print(f"已保存MAT文件: {mat_path}")
        
        return csv_path, mat_path
    
    def plot_training_history(self):
        """绘制训练历史"""
        if not self.training_history:
            print("没有训练历史数据")
            return
        
        history_df = pd.DataFrame(self.training_history)
        
        plt.figure(figsize=(15, 10))
        
        # 损失曲线
        plt.subplot(2, 2, 1)
        plt.plot(history_df['epoch'], history_df['d_loss'], label='Discriminator Loss')
        plt.plot(history_df['epoch'], history_df['g_loss'], label='Generator Loss')
        plt.title('Training Losses')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.legend()
        
        # Wasserstein距离
        plt.subplot(2, 2, 2)
        plt.plot(history_df['epoch'], history_df['wasserstein_distance'])
        plt.title('Wasserstein Distance')
        plt.xlabel('Epoch')
        plt.ylabel('Distance')
        
        # 梯度惩罚
        plt.subplot(2, 2, 3)
        plt.plot(history_df['epoch'], history_df['gradient_penalty'])
        plt.title('Gradient Penalty')
        plt.xlabel('Epoch')
        plt.ylabel('Penalty')
        
        # 物理约束损失
        plt.subplot(2, 2, 4)
        plt.plot(history_df['epoch'], history_df['physical_loss'])
        plt.title('Physical Constraint Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        
        plt.tight_layout()
        plt.savefig('demo_results/demo_training_history.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("训练历史图已保存为 demo_results/demo_training_history.png")

def main():
    """主函数"""
    print("=== WiFi指纹数据集扩充项目 - 演示版本 ===")
    print(f"开始时间: {datetime.now()}")
    
    # 创建训练器
    trainer = DemoWiFiGANTrainer()
    
    # 1. 训练模型
    print("\n1. 开始训练模型...")
    trainer.train_model(epochs=30, batch_size=64)
    
    # 2. 评估模型
    print("\n2. 评估模型...")
    score, is_acceptable, results = trainer.evaluate_current_model()
    
    # 3. 生成数据
    print("\n3. 生成WiFi指纹数据...")
    generated_data = trainer.generate_final_data(num_samples=1391)
    
    # 4. 合并数据
    print("\n4. 合并原始数据和生成数据...")
    merged_data = trainer.merge_with_original_data(generated_data)
    
    # 5. 绘制训练历史
    print("\n5. 绘制训练历史...")
    trainer.plot_training_history()
    
    # 6. 保存项目总结
    summary = {
        'project': 'WiFi指纹数据集扩充（演示版本）',
        'method': 'WGAN-GP',
        'building': 0,
        'floor': 3,
        'original_samples': 1391,
        'generated_samples': 1391,
        'total_samples': len(merged_data),
        'wap_features': 520,
        'position_features': 2,
        'hyperparameters': trainer.hyperparams,
        'evaluation_results': results,
        'final_score': score,
        'quality_acceptable': is_acceptable,
        'completion_time': datetime.now().isoformat()
    }
    
    import json
    with open('demo_results/demo_project_summary.json', 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)
    
    print("\n=== 演示项目完成 ===")
    print(f"完成时间: {datetime.now()}")
    print(f"生成数据质量是否可接受: {'是' if is_acceptable else '否'}")
    print(f"最终得分: {score:.4f}")
    print("所有结果文件已保存到 demo_results/ 目录")
    print(f"最终数据集包含 {len(merged_data)} 条记录")
    print("- 原始数据: 1391 条")
    print("- 生成数据: 1391 条")
    print("- WAP特征: 520 个")
    print("- 位置特征: 2 个")
    
    # 显示评价指标
    print("\n=== 评价指标结果 ===")
    for key, value in results.items():
        if isinstance(value, float):
            print(f"{key}: {value:.4f}")
    
    print("\n注意：这是演示版本，使用了较少的训练epoch。")
    print("要获得更好的结果，请运行 final_training.py 进行完整训练。")

if __name__ == "__main__":
    main()
