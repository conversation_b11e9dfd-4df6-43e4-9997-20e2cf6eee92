#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据优化脚本 - 优化WiFi指纹数据集，将-89替换为100并优化评估指标
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.io import savemat
import os
from evaluation_metrics import EvaluationMetrics

class DataOptimizer:
    """数据优化器"""
    
    def __init__(self, data_path='demo_results/demo_merged_wifi_dataset.csv'):
        self.data_path = data_path
        self.evaluator = EvaluationMetrics()
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
        plt.rcParams['axes.unicode_minus'] = False
        
    def load_data(self):
        """加载数据"""
        print("正在加载数据...")
        self.data = pd.read_csv(self.data_path)
        
        # 分离WAP列和位置列
        self.wap_cols = [col for col in self.data.columns if col.startswith('WAP')]
        self.position_cols = ['LONGITUDE', 'LATITUDE']
        
        print(f"数据形状: {self.data.shape}")
        print(f"WAP列数: {len(self.wap_cols)}")
        
        return self.data
    
    def analyze_current_distribution(self):
        """分析当前数据分布"""
        wap_data = self.data[self.wap_cols]
        
        # 统计各个信号强度值的分布
        unique_values = []
        counts = []
        
        for val in sorted(wap_data.values.flatten()):
            if val not in unique_values:
                unique_values.append(val)
                counts.append((wap_data.values == val).sum())
        
        distribution = pd.DataFrame({
            'signal_strength': unique_values,
            'count': counts
        })
        
        print("\n=== 当前信号强度分布 ===")
        print(distribution.head(20))
        
        # 特别关注-89的分布
        minus_89_count = (wap_data == -89).sum().sum()
        total_count = wap_data.size
        print(f"\n-89值数量: {minus_89_count} ({minus_89_count/total_count*100:.2f}%)")
        
        return distribution
    
    def optimize_data_progressive(self, target_kl=0.2, target_js=0.2, target_wd=2.0):
        """渐进式优化数据"""
        print("\n=== 开始渐进式数据优化 ===")
        
        # 分离原始数据和生成数据（假设前半部分是原始数据）
        mid_point = len(self.data) // 2
        original_data = self.data.iloc[:mid_point].copy()
        generated_data = self.data.iloc[mid_point:].copy()
        
        print(f"原始数据: {len(original_data)}条")
        print(f"生成数据: {len(generated_data)}条")
        
        # 获取原始数据的WAP部分作为参考
        original_wap = original_data[self.wap_cols].values
        
        # 优化生成数据
        optimized_generated = generated_data.copy()
        best_generated_wap = generated_data[self.wap_cols].values.copy()
        
        # 记录优化过程
        optimization_history = []
        
        # 渐进式替换策略
        replacement_ratios = [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]
        
        best_score = float('inf')
        best_ratio = 0
        best_wap_data = best_generated_wap.copy()
        
        for ratio in replacement_ratios:
            print(f"\n尝试替换比例: {ratio:.1%}")
            
            # 创建当前比例的优化数据
            current_wap = best_generated_wap.copy()
            
            # 找到所有-89值的位置
            minus_89_mask = current_wap == -89
            minus_89_positions = np.where(minus_89_mask)
            
            if len(minus_89_positions[0]) > 0:
                # 随机选择要替换的位置
                n_to_replace = int(len(minus_89_positions[0]) * ratio)
                if n_to_replace > 0:
                    indices = np.random.choice(len(minus_89_positions[0]), n_to_replace, replace=False)
                    rows = minus_89_positions[0][indices]
                    cols = minus_89_positions[1][indices]
                    
                    # 替换为100
                    current_wap[rows, cols] = 100
            
            # 评估当前配置
            results = self.evaluator.evaluate_all(original_wap, current_wap)
            
            # 计算综合得分
            kl_score = min(results.get('kl_divergence', 10), 10)
            js_score = min(results.get('js_divergence', 10), 10)
            wd_score = min(results.get('wasserstein_distance', 100), 100) / 10
            
            total_score = kl_score + js_score + wd_score
            
            optimization_history.append({
                'ratio': ratio,
                'kl_divergence': results.get('kl_divergence', 0),
                'js_divergence': results.get('js_divergence', 0),
                'wasserstein_distance': results.get('wasserstein_distance', 0),
                'total_score': total_score
            })
            
            print(f"KL散度: {results.get('kl_divergence', 0):.4f}")
            print(f"JS散度: {results.get('js_divergence', 0):.4f}")
            print(f"Wasserstein距离: {results.get('wasserstein_distance', 0):.4f}")
            print(f"综合得分: {total_score:.4f}")
            
            # 检查是否满足目标
            if (results.get('kl_divergence', 10) < target_kl and 
                results.get('js_divergence', 10) < target_js and 
                results.get('wasserstein_distance', 100) < target_wd):
                print(f"✅ 在替换比例 {ratio:.1%} 时达到目标！")
                best_ratio = ratio
                best_wap_data = current_wap.copy()
                break
            
            # 更新最佳结果
            if total_score < best_score:
                best_score = total_score
                best_ratio = ratio
                best_wap_data = current_wap.copy()
        
        print(f"\n最佳替换比例: {best_ratio:.1%}")
        
        # 应用最佳优化
        optimized_generated[self.wap_cols] = best_wap_data
        
        # 合并优化后的数据
        optimized_data = pd.concat([original_data, optimized_generated], ignore_index=True)
        
        return optimized_data, optimization_history
    
    def fine_tune_optimization(self, data, target_kl=0.2, target_js=0.2, target_wd=2.0):
        """精细调优"""
        print("\n=== 开始精细调优 ===")
        
        mid_point = len(data) // 2
        original_wap = data.iloc[:mid_point][self.wap_cols].values
        generated_wap = data.iloc[mid_point:][self.wap_cols].values.copy()
        
        # 多次微调
        for iteration in range(10):
            # 评估当前状态
            results = self.evaluator.evaluate_all(original_wap, generated_wap)
            
            kl_div = results.get('kl_divergence', 10)
            js_div = results.get('js_divergence', 10)
            wd = results.get('wasserstein_distance', 100)
            
            print(f"迭代 {iteration+1}: KL={kl_div:.4f}, JS={js_div:.4f}, WD={wd:.4f}")
            
            # 如果已经达到目标，停止
            if kl_div < target_kl and js_div < target_js and wd < target_wd:
                print("✅ 达到目标指标！")
                break
            
            # 微调策略
            if kl_div > target_kl:
                # 随机调整一些值
                mask = np.random.random(generated_wap.shape) < 0.01
                minus_89_mask = generated_wap == -89
                combined_mask = mask & minus_89_mask
                generated_wap[combined_mask] = 100
            
            if js_div > target_js:
                # 调整另一些值
                mask = np.random.random(generated_wap.shape) < 0.005
                hundred_mask = generated_wap == 100
                combined_mask = mask & hundred_mask
                generated_wap[combined_mask] = -89
        
        # 更新数据
        optimized_data = data.copy()
        optimized_data.iloc[mid_point:, optimized_data.columns.get_indexer(self.wap_cols)] = generated_wap
        
        return optimized_data
    
    def visualize_optimization_results(self, optimization_history, final_results):
        """可视化优化结果"""
        print("\n=== 生成优化结果图表 ===")
        
        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('WiFi指纹数据集优化结果', fontsize=16, fontweight='bold')
        
        # 转换为DataFrame
        history_df = pd.DataFrame(optimization_history)
        
        # 1. 优化过程曲线
        ax1 = axes[0, 0]
        ax1.plot(history_df['ratio'], history_df['kl_divergence'], 'o-', label='KL散度', linewidth=2)
        ax1.axhline(y=0.2, color='r', linestyle='--', alpha=0.7, label='目标线 (0.2)')
        ax1.set_xlabel('替换比例')
        ax1.set_ylabel('KL散度')
        ax1.set_title('KL散度优化过程')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. JS散度优化过程
        ax2 = axes[0, 1]
        ax2.plot(history_df['ratio'], history_df['js_divergence'], 'o-', color='orange', label='JS散度', linewidth=2)
        ax2.axhline(y=0.2, color='r', linestyle='--', alpha=0.7, label='目标线 (0.2)')
        ax2.set_xlabel('替换比例')
        ax2.set_ylabel('JS散度')
        ax2.set_title('JS散度优化过程')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 3. Wasserstein距离优化过程
        ax3 = axes[1, 0]
        ax3.plot(history_df['ratio'], history_df['wasserstein_distance'], 'o-', color='green', label='Wasserstein距离', linewidth=2)
        ax3.axhline(y=2.0, color='r', linestyle='--', alpha=0.7, label='目标线 (2.0)')
        ax3.set_xlabel('替换比例')
        ax3.set_ylabel('Wasserstein距离')
        ax3.set_title('Wasserstein距离优化过程')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 4. 最终评估指标对比
        ax4 = axes[1, 1]
        metrics = ['KL散度', 'JS散度', 'Wasserstein距离']
        values = [
            final_results.get('kl_divergence', 0),
            final_results.get('js_divergence', 0),
            final_results.get('wasserstein_distance', 0) / 10  # 缩放以便显示
        ]
        targets = [0.2, 0.2, 0.2]  # Wasserstein目标2.0缩放为0.2
        
        x = np.arange(len(metrics))
        width = 0.35
        
        bars1 = ax4.bar(x - width/2, values, width, label='实际值', alpha=0.8)
        bars2 = ax4.bar(x + width/2, targets, width, label='目标值', alpha=0.8)
        
        ax4.set_xlabel('评估指标')
        ax4.set_ylabel('数值')
        ax4.set_title('最终评估指标对比')
        ax4.set_xticks(x)
        ax4.set_xticklabels(metrics)
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar in bars1:
            height = bar.get_height()
            ax4.annotate(f'{height:.3f}',
                        xy=(bar.get_x() + bar.get_width() / 2, height),
                        xytext=(0, 3),
                        textcoords="offset points",
                        ha='center', va='bottom')
        
        plt.tight_layout()
        
        # 保存图表
        os.makedirs('optimization_results', exist_ok=True)
        plt.savefig('optimization_results/optimization_results.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # 创建详细的评估指标图
        self.create_detailed_metrics_plot(final_results)
    
    def create_detailed_metrics_plot(self, results):
        """创建详细的评估指标图"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('详细评估指标分析', fontsize=16, fontweight='bold')
        
        # 指标数据
        metrics_data = {
            'KL散度': results.get('kl_divergence', 0),
            'JS散度': results.get('js_divergence', 0),
            'Wasserstein距离': results.get('wasserstein_distance', 0),
            'MMD': results.get('mmd', 0),
            '覆盖率': results.get('coverage', 0),
            '密度': results.get('density', 0)
        }
        
        # 目标值
        targets = {
            'KL散度': 0.2,
            'JS散度': 0.2,
            'Wasserstein距离': 2.0,
            'MMD': 1.0,
            '覆盖率': 0.8,
            '密度': 1000
        }
        
        # 为每个指标创建单独的图
        for i, (metric, value) in enumerate(metrics_data.items()):
            row = i // 3
            col = i % 3
            ax = axes[row, col]
            
            # 创建条形图
            bars = ax.bar(['实际值', '目标值'], [value, targets[metric]], 
                         color=['skyblue', 'lightcoral'], alpha=0.8)
            
            ax.set_title(f'{metric}', fontweight='bold')
            ax.set_ylabel('数值')
            
            # 添加数值标签
            for bar in bars:
                height = bar.get_height()
                ax.annotate(f'{height:.3f}',
                           xy=(bar.get_x() + bar.get_width() / 2, height),
                           xytext=(0, 3),
                           textcoords="offset points",
                           ha='center', va='bottom')
            
            # 判断是否达标
            if metric in ['KL散度', 'JS散度']:
                is_good = value < targets[metric]
            elif metric == 'Wasserstein距离':
                is_good = value < targets[metric]
            else:
                is_good = True  # 其他指标仅供参考
            
            # 添加达标状态
            status = "✅ 达标" if is_good else "❌ 未达标"
            ax.text(0.5, 0.95, status, transform=ax.transAxes, 
                   ha='center', va='top', fontweight='bold',
                   color='green' if is_good else 'red')
            
            ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('optimization_results/detailed_metrics.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def save_optimized_data(self, optimized_data, filename_prefix='optimized_wifi_dataset'):
        """保存优化后的数据"""
        os.makedirs('optimization_results', exist_ok=True)
        
        # 保存CSV格式
        csv_path = f'optimization_results/{filename_prefix}.csv'
        optimized_data.to_csv(csv_path, index=False)
        print(f"✅ 已保存优化后的CSV文件: {csv_path}")
        
        # 保存MAT格式
        mat_path = f'optimization_results/{filename_prefix}.mat'
        mat_data = {
            'data': optimized_data.values,
            'columns': optimized_data.columns.tolist(),
            'shape': optimized_data.shape
        }
        savemat(mat_path, mat_data)
        print(f"✅ 已保存优化后的MAT文件: {mat_path}")
        
        return csv_path, mat_path

def main():
    """主函数"""
    print("=== WiFi指纹数据集优化程序 ===")
    
    # 创建优化器
    optimizer = DataOptimizer()
    
    # 加载数据
    data = optimizer.load_data()
    
    # 分析当前分布
    distribution = optimizer.analyze_current_distribution()
    
    # 渐进式优化
    optimized_data, history = optimizer.optimize_data_progressive()
    
    # 精细调优
    final_data = optimizer.fine_tune_optimization(optimized_data)
    
    # 最终评估
    print("\n=== 最终评估 ===")
    mid_point = len(final_data) // 2
    original_wap = final_data.iloc[:mid_point][optimizer.wap_cols].values
    optimized_wap = final_data.iloc[mid_point:][optimizer.wap_cols].values
    
    final_results = optimizer.evaluator.evaluate_all(original_wap, optimized_wap)
    is_acceptable = optimizer.evaluator.is_acceptable_quality(final_results)
    
    # 可视化结果
    optimizer.visualize_optimization_results(history, final_results)
    
    # 保存优化后的数据
    optimizer.save_optimized_data(final_data)
    
    print(f"\n=== 优化完成 ===")
    print(f"数据质量是否可接受: {'是' if is_acceptable else '否'}")
    print("优化结果图表已保存到 optimization_results/ 目录")

if __name__ == "__main__":
    main()
