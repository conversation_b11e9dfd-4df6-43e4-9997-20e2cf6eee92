#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WGAN-GP模型实现 - 用于WiFi指纹数据生成
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.autograd as autograd
import numpy as np
import pandas as pd
from torch.utils.data import DataLoader, TensorDataset
import pickle
import os

class Generator(nn.Module):
    """生成器网络"""
    def __init__(self, noise_dim=100, output_dim=520, hidden_dims=[256, 512, 1024]):
        super(Generator, self).__init__()
        
        layers = []
        input_dim = noise_dim
        
        # 构建隐藏层
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(input_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.ReLU(True)
            ])
            input_dim = hidden_dim
        
        # 输出层
        layers.append(nn.Linear(input_dim, output_dim))
        layers.append(nn.Tanh())  # 输出范围 [-1, 1]
        
        self.model = nn.Sequential(*layers)
        
    def forward(self, z):
        return self.model(z)

class Discriminator(nn.Module):
    """判别器网络"""
    def __init__(self, input_dim=520, hidden_dims=[1024, 512, 256]):
        super(Discriminator, self).__init__()
        
        layers = []
        current_dim = input_dim
        
        # 构建隐藏层
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(current_dim, hidden_dim),
                nn.LeakyReLU(0.2, True),
                nn.Dropout(0.3)
            ])
            current_dim = hidden_dim
        
        # 输出层
        layers.append(nn.Linear(current_dim, 1))
        
        self.model = nn.Sequential(*layers)
        
    def forward(self, x):
        return self.model(x)

class WGAN_GP:
    """WGAN-GP模型类"""
    def __init__(self, noise_dim=100, data_dim=520, device='cuda' if torch.cuda.is_available() else 'cpu'):
        self.device = device
        self.noise_dim = noise_dim
        self.data_dim = data_dim
        
        # 初始化网络
        self.generator = Generator(noise_dim, data_dim).to(device)
        self.discriminator = Discriminator(data_dim).to(device)
        
        # 初始化优化器（稍后设置学习率）
        self.g_optimizer = None
        self.d_optimizer = None
        
        print(f"模型初始化完成，使用设备: {device}")
        print(f"生成器参数数量: {sum(p.numel() for p in self.generator.parameters())}")
        print(f"判别器参数数量: {sum(p.numel() for p in self.discriminator.parameters())}")
    
    def set_optimizers(self, lr=0.0001):
        """设置优化器"""
        self.g_optimizer = optim.Adam(self.generator.parameters(), lr=lr, betas=(0.5, 0.9))
        self.d_optimizer = optim.Adam(self.discriminator.parameters(), lr=lr, betas=(0.5, 0.9))
    
    def gradient_penalty(self, real_data, fake_data, lambda_gp=10):
        """计算梯度惩罚"""
        batch_size = real_data.size(0)
        
        # 随机插值
        alpha = torch.rand(batch_size, 1).to(self.device)
        alpha = alpha.expand_as(real_data)
        
        interpolated = alpha * real_data + (1 - alpha) * fake_data
        interpolated = interpolated.to(self.device)
        interpolated.requires_grad_(True)
        
        # 计算判别器输出
        d_interpolated = self.discriminator(interpolated)
        
        # 计算梯度
        gradients = autograd.grad(
            outputs=d_interpolated,
            inputs=interpolated,
            grad_outputs=torch.ones_like(d_interpolated),
            create_graph=True,
            retain_graph=True,
            only_inputs=True
        )[0]
        
        # 计算梯度惩罚
        gradients = gradients.view(batch_size, -1)
        gradient_penalty = lambda_gp * ((gradients.norm(2, dim=1) - 1) ** 2).mean()
        
        return gradient_penalty
    
    def physical_constraint_loss(self, generated_data, lambda_physical=1.0):
        """物理约束损失 - 确保生成的信号强度在合理范围内"""
        # WiFi信号强度通常在-100到-30 dBm之间
        # 由于数据已经标准化，我们需要相应调整约束范围
        
        # 计算超出合理范围的惩罚
        # 对于标准化后的数据，我们设置一个合理的范围
        min_val = -3.0  # 对应约-100 dBm
        max_val = 3.0   # 对应约-30 dBm
        
        # 计算超出范围的惩罚
        penalty = torch.mean(torch.relu(generated_data - max_val)) + \
                 torch.mean(torch.relu(min_val - generated_data))
        
        return lambda_physical * penalty
    
    def train_step(self, real_data, lambda_gp=10, lambda_physical=1.0, n_critic=5):
        """训练一步"""
        batch_size = real_data.size(0)
        
        # 训练判别器
        for _ in range(n_critic):
            self.d_optimizer.zero_grad()
            
            # 真实数据
            real_validity = self.discriminator(real_data)
            
            # 生成假数据
            z = torch.randn(batch_size, self.noise_dim).to(self.device)
            fake_data = self.generator(z)
            fake_validity = self.discriminator(fake_data.detach())
            
            # 计算Wasserstein损失
            d_loss = torch.mean(fake_validity) - torch.mean(real_validity)
            
            # 添加梯度惩罚
            gp = self.gradient_penalty(real_data, fake_data, lambda_gp)
            d_loss += gp
            
            d_loss.backward()
            self.d_optimizer.step()
        
        # 训练生成器
        self.g_optimizer.zero_grad()
        
        z = torch.randn(batch_size, self.noise_dim).to(self.device)
        fake_data = self.generator(z)
        fake_validity = self.discriminator(fake_data)
        
        # 生成器损失
        g_loss = -torch.mean(fake_validity)
        
        # 添加物理约束
        physical_loss = self.physical_constraint_loss(fake_data, lambda_physical)
        g_loss += physical_loss
        
        g_loss.backward()
        self.g_optimizer.step()
        
        return {
            'd_loss': d_loss.item(),
            'g_loss': g_loss.item(),
            'gradient_penalty': gp.item(),
            'physical_loss': physical_loss.item(),
            'wasserstein_distance': (torch.mean(real_validity) - torch.mean(fake_validity)).item()
        }
    
    def generate_samples(self, num_samples):
        """生成样本"""
        self.generator.eval()
        with torch.no_grad():
            z = torch.randn(num_samples, self.noise_dim).to(self.device)
            generated = self.generator(z)
        self.generator.train()
        return generated.cpu().numpy()
    
    def save_model(self, path):
        """保存模型"""
        torch.save({
            'generator_state_dict': self.generator.state_dict(),
            'discriminator_state_dict': self.discriminator.state_dict(),
            'g_optimizer_state_dict': self.g_optimizer.state_dict(),
            'd_optimizer_state_dict': self.d_optimizer.state_dict(),
        }, path)
        print(f"模型已保存到: {path}")
    
    def load_model(self, path):
        """加载模型"""
        checkpoint = torch.load(path, map_location=self.device)
        self.generator.load_state_dict(checkpoint['generator_state_dict'])
        self.discriminator.load_state_dict(checkpoint['discriminator_state_dict'])
        if self.g_optimizer:
            self.g_optimizer.load_state_dict(checkpoint['g_optimizer_state_dict'])
        if self.d_optimizer:
            self.d_optimizer.load_state_dict(checkpoint['d_optimizer_state_dict'])
        print(f"模型已从 {path} 加载")

def load_training_data(data_path='processed_data/building0_floor3_training.csv'):
    """加载训练数据"""
    df = pd.read_csv(data_path)
    
    # 只使用WAP数据进行训练
    wap_cols = [col for col in df.columns if col.startswith('WAP')]
    wap_data = df[wap_cols].values
    
    print(f"加载训练数据: {wap_data.shape}")
    print(f"数据范围: {wap_data.min():.2f} 到 {wap_data.max():.2f}")
    
    return wap_data

if __name__ == "__main__":
    # 测试模型
    print("=== 测试WGAN-GP模型 ===")
    
    # 加载数据
    data = load_training_data()
    
    # 创建模型
    model = WGAN_GP()
    model.set_optimizers(lr=0.0001)
    
    # 创建数据加载器
    tensor_data = torch.FloatTensor(data)
    dataset = TensorDataset(tensor_data)
    dataloader = DataLoader(dataset, batch_size=64, shuffle=True)
    
    print("模型测试完成！")
