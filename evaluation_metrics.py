#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
评价指标模块 - 计算KL散度、JS散度、Wasserstein距离
"""

import numpy as np
import torch
from scipy import stats
from scipy.spatial.distance import j<PERSON><PERSON><PERSON><PERSON>
from scipy.stats import wasserstein_distance
from sklearn.neighbors import NearestNeighbors
import warnings
warnings.filterwarnings('ignore')

class EvaluationMetrics:
    """评价指标计算类"""
    
    def __init__(self):
        self.epsilon = 1e-8  # 防止除零
    
    def compute_kl_divergence(self, real_data, generated_data, bins=50):
        """计算KL散度"""
        kl_divs = []
        
        for i in range(real_data.shape[1]):  # 对每个特征计算
            real_feature = real_data[:, i]
            gen_feature = generated_data[:, i]
            
            # 计算直方图
            min_val = min(real_feature.min(), gen_feature.min())
            max_val = max(real_feature.max(), gen_feature.max())
            
            if min_val == max_val:  # 如果特征值都相同，跳过
                continue
                
            bin_edges = np.linspace(min_val, max_val, bins + 1)
            
            real_hist, _ = np.histogram(real_feature, bins=bin_edges, density=True)
            gen_hist, _ = np.histogram(gen_feature, bins=bin_edges, density=True)
            
            # 归一化为概率分布
            real_hist = real_hist / (real_hist.sum() + self.epsilon)
            gen_hist = gen_hist / (gen_hist.sum() + self.epsilon)
            
            # 添加小的epsilon避免log(0)
            real_hist = real_hist + self.epsilon
            gen_hist = gen_hist + self.epsilon
            
            # 计算KL散度
            kl_div = np.sum(real_hist * np.log(real_hist / gen_hist))
            kl_divs.append(kl_div)
        
        return np.mean(kl_divs)
    
    def compute_js_divergence(self, real_data, generated_data, bins=50):
        """计算JS散度"""
        js_divs = []
        
        for i in range(real_data.shape[1]):
            real_feature = real_data[:, i]
            gen_feature = generated_data[:, i]
            
            # 计算直方图
            min_val = min(real_feature.min(), gen_feature.min())
            max_val = max(real_feature.max(), gen_feature.max())
            
            if min_val == max_val:
                continue
                
            bin_edges = np.linspace(min_val, max_val, bins + 1)
            
            real_hist, _ = np.histogram(real_feature, bins=bin_edges, density=True)
            gen_hist, _ = np.histogram(gen_feature, bins=bin_edges, density=True)
            
            # 归一化
            real_hist = real_hist / (real_hist.sum() + self.epsilon)
            gen_hist = gen_hist / (gen_hist.sum() + self.epsilon)
            
            # 计算JS散度
            js_div = jensenshannon(real_hist, gen_hist) ** 2
            js_divs.append(js_div)
        
        return np.mean(js_divs)
    
    def compute_wasserstein_distance(self, real_data, generated_data, sample_size=1000):
        """计算Wasserstein距离"""
        # 由于计算复杂度，我们随机采样一部分数据
        if len(real_data) > sample_size:
            real_indices = np.random.choice(len(real_data), sample_size, replace=False)
            real_sample = real_data[real_indices]
        else:
            real_sample = real_data
            
        if len(generated_data) > sample_size:
            gen_indices = np.random.choice(len(generated_data), sample_size, replace=False)
            gen_sample = generated_data[gen_indices]
        else:
            gen_sample = generated_data
        
        wasserstein_dists = []
        
        for i in range(real_sample.shape[1]):
            real_feature = real_sample[:, i]
            gen_feature = gen_sample[:, i]
            
            # 计算一维Wasserstein距离
            wd = wasserstein_distance(real_feature, gen_feature)
            wasserstein_dists.append(wd)
        
        return np.mean(wasserstein_dists)
    
    def compute_mmd(self, real_data, generated_data, sigma=1.0):
        """计算最大均值差异(MMD)"""
        def rbf_kernel(x, y, sigma):
            """RBF核函数"""
            return np.exp(-np.linalg.norm(x - y) ** 2 / (2 * sigma ** 2))
        
        n_real = len(real_data)
        n_gen = len(generated_data)
        
        # 计算核矩阵
        k_rr = 0
        for i in range(n_real):
            for j in range(n_real):
                k_rr += rbf_kernel(real_data[i], real_data[j], sigma)
        k_rr /= (n_real * n_real)
        
        k_gg = 0
        for i in range(n_gen):
            for j in range(n_gen):
                k_gg += rbf_kernel(generated_data[i], generated_data[j], sigma)
        k_gg /= (n_gen * n_gen)
        
        k_rg = 0
        for i in range(n_real):
            for j in range(n_gen):
                k_rg += rbf_kernel(real_data[i], generated_data[j], sigma)
        k_rg /= (n_real * n_gen)
        
        mmd = k_rr + k_gg - 2 * k_rg
        return mmd
    
    def compute_coverage_and_density(self, real_data, generated_data, k=5):
        """计算覆盖率和密度指标"""
        # 使用k近邻计算
        nbrs_real = NearestNeighbors(n_neighbors=k+1).fit(real_data)
        nbrs_gen = NearestNeighbors(n_neighbors=k+1).fit(generated_data)
        
        # 计算覆盖率 (Coverage)
        distances_real_to_gen, _ = nbrs_gen.kneighbors(real_data)
        distances_gen_to_real, _ = nbrs_real.kneighbors(generated_data)
        
        # 计算每个真实样本到生成样本的最近距离
        min_distances_real_to_gen = distances_real_to_gen[:, 0]
        # 计算每个生成样本到真实样本的最近距离
        min_distances_gen_to_real = distances_gen_to_real[:, 0]
        
        # 覆盖率：被生成数据覆盖的真实数据比例
        threshold = np.percentile(min_distances_real_to_gen, 50)
        coverage = np.mean(min_distances_real_to_gen <= threshold)
        
        # 密度：生成数据的质量
        density = np.mean(min_distances_gen_to_real)
        
        return coverage, density
    
    def evaluate_all(self, real_data, generated_data):
        """计算所有评价指标"""
        print("正在计算评价指标...")
        
        # 确保数据是numpy数组
        if torch.is_tensor(real_data):
            real_data = real_data.cpu().numpy()
        if torch.is_tensor(generated_data):
            generated_data = generated_data.cpu().numpy()
        
        results = {}
        
        # KL散度
        try:
            kl_div = self.compute_kl_divergence(real_data, generated_data)
            results['kl_divergence'] = kl_div
            print(f"KL散度: {kl_div:.4f}")
        except Exception as e:
            print(f"KL散度计算失败: {e}")
            results['kl_divergence'] = float('inf')
        
        # JS散度
        try:
            js_div = self.compute_js_divergence(real_data, generated_data)
            results['js_divergence'] = js_div
            print(f"JS散度: {js_div:.4f}")
        except Exception as e:
            print(f"JS散度计算失败: {e}")
            results['js_divergence'] = float('inf')
        
        # Wasserstein距离
        try:
            wd = self.compute_wasserstein_distance(real_data, generated_data)
            results['wasserstein_distance'] = wd
            print(f"Wasserstein距离: {wd:.4f}")
        except Exception as e:
            print(f"Wasserstein距离计算失败: {e}")
            results['wasserstein_distance'] = float('inf')
        
        # MMD
        try:
            mmd = self.compute_mmd(real_data[:500], generated_data[:500])  # 采样计算
            results['mmd'] = mmd
            print(f"MMD: {mmd:.4f}")
        except Exception as e:
            print(f"MMD计算失败: {e}")
            results['mmd'] = float('inf')
        
        # 覆盖率和密度
        try:
            coverage, density = self.compute_coverage_and_density(real_data[:500], generated_data[:500])
            results['coverage'] = coverage
            results['density'] = density
            print(f"覆盖率: {coverage:.4f}")
            print(f"密度: {density:.4f}")
        except Exception as e:
            print(f"覆盖率和密度计算失败: {e}")
            results['coverage'] = 0.0
            results['density'] = float('inf')
        
        return results
    
    def is_acceptable_quality(self, results, kl_threshold=0.2, js_threshold=0.2, wd_threshold=2.0):
        """判断生成质量是否可接受"""
        kl_ok = results.get('kl_divergence', float('inf')) < kl_threshold
        js_ok = results.get('js_divergence', float('inf')) < js_threshold
        wd_ok = results.get('wasserstein_distance', float('inf')) < wd_threshold
        
        print(f"\n质量评估:")
        print(f"KL散度 < {kl_threshold}: {'✓' if kl_ok else '✗'}")
        print(f"JS散度 < {js_threshold}: {'✓' if js_ok else '✗'}")
        print(f"Wasserstein距离 < {wd_threshold}: {'✓' if wd_ok else '✗'}")
        
        return kl_ok and js_ok and wd_ok

if __name__ == "__main__":
    # 测试评价指标
    print("=== 测试评价指标 ===")
    
    # 生成测试数据
    np.random.seed(42)
    real_data = np.random.randn(1000, 10)
    generated_data = np.random.randn(800, 10) + 0.1  # 稍微不同的分布
    
    # 计算指标
    evaluator = EvaluationMetrics()
    results = evaluator.evaluate_all(real_data, generated_data)
    
    # 判断质量
    is_good = evaluator.is_acceptable_quality(results)
    print(f"\n生成质量是否可接受: {'是' if is_good else '否'}")
